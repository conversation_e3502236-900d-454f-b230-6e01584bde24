import React from 'react';

const EditSvg = () => {
    return (
        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.61558 1C5.74906 1 5.87706 1.05268 5.97144 1.14645C6.06582 1.24021 6.11884 1.36739 6.11884 1.5V2H8.13189V1.5C8.13189 1.36739 8.18491 1.24021 8.27929 1.14645C8.37367 1.05268 8.50167 1 8.63515 1C8.76862 1 8.89663 1.05268 8.99101 1.14645C9.08539 1.24021 9.13841 1.36739 9.13841 1.5V2H11.1515V1.5C11.1515 1.36739 11.2045 1.24021 11.2989 1.14645C11.3932 1.05268 11.5212 1 11.6547 1C11.7882 1 11.9162 1.05268 12.0106 1.14645C12.105 1.24021 12.158 1.36739 12.158 1.5V2C12.5584 2 12.9424 2.15804 13.2256 2.43934C13.5087 2.72064 13.6678 3.10218 13.6678 3.5V6.036C13.3069 6.09834 12.9637 6.23679 12.6612 6.442V3.5C12.6612 3.36739 12.6082 3.24021 12.5138 3.14645C12.4195 3.05268 12.2914 3 12.158 3H5.11232C4.97885 3 4.85084 3.05268 4.75646 3.14645C4.66208 3.24021 4.60906 3.36739 4.60906 3.5V13.5C4.60906 13.6326 4.66208 13.7598 4.75646 13.8536C4.85084 13.9473 4.97885 14 5.11232 14H6.66941V14.002C6.58356 14.3333 6.60756 14.6833 6.73785 15H5.11232C4.7119 15 4.32788 14.842 4.04474 14.5607C3.76161 14.2794 3.60254 13.8978 3.60254 13.5V3.5C3.60254 3.10218 3.76161 2.72064 4.04474 2.43934C4.32788 2.15804 4.7119 2 5.11232 2V1.5C5.11232 1.36739 5.16534 1.24021 5.25972 1.14645C5.3541 1.05268 5.48211 1 5.61558 1ZM10.6482 8C10.7559 8 10.8555 8.034 10.9381 8.091L10.0221 9H6.6221C6.48863 9 6.36062 8.94732 6.26625 8.85355C6.17187 8.75979 6.11884 8.63261 6.11884 8.5C6.11884 8.36739 6.17187 8.24021 6.26625 8.14645C6.36062 8.05268 6.48863 8 6.6221 8H10.6482ZM7.6246 11.435C7.60872 11.3146 7.5493 11.2041 7.45744 11.1241C7.36558 11.044 7.24755 10.9999 7.12537 11H6.6221C6.48863 11 6.36062 11.0527 6.26625 11.1464C6.17187 11.2402 6.11884 11.3674 6.11884 11.5C6.11884 11.6326 6.17187 11.7598 6.26625 11.8536C6.36062 11.9473 6.48863 12 6.6221 12H7.12537C7.17905 12.0001 7.2324 11.9917 7.28339 11.975C7.38102 11.786 7.49476 11.605 7.6246 11.435ZM6.6221 5C6.48863 5 6.36062 5.05268 6.26625 5.14645C6.17187 5.24021 6.11884 5.36739 6.11884 5.5C6.11884 5.63261 6.17187 5.75979 6.26625 5.85355C6.36062 5.94732 6.48863 6 6.6221 6H10.6482C10.7817 6 10.9097 5.94732 11.0041 5.85355C11.0984 5.75979 11.1515 5.63261 11.1515 5.5C11.1515 5.36739 11.0984 5.24021 11.0041 5.14645C10.9097 5.05268 10.7817 5 10.6482 5H6.6221ZM13.0014 7.455C13.296 7.16233 13.6955 6.99792 14.1121 6.99792C14.5287 6.99792 14.9283 7.16233 15.2228 7.455C15.5174 7.74767 15.6829 8.14461 15.6829 8.5585C15.6829 8.97239 15.5174 9.36933 15.2228 9.662L10.9059 13.95C10.5477 14.3061 10.0989 14.5588 9.60745 14.681L8.38855 14.984C8.28565 15.0096 8.17781 15.0083 8.07557 14.9802C7.97332 14.9521 7.88015 14.8982 7.80513 14.8236C7.73012 14.7491 7.67582 14.6565 7.64753 14.555C7.61924 14.4534 7.61793 14.3462 7.64372 14.244L7.94971 13.034C8.0725 12.545 8.32615 12.099 8.68447 11.744L13.0014 7.455Z" fill="#FF7918"/>
        </svg>

    );
};

export default EditSvg;
