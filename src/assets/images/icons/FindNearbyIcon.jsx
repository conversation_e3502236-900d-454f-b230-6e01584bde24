import React from 'react'

const FindNearbyIcon = () => {
    return (
        <svg
            width="109"
            height="112"
            viewBox="0 0 109 112"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <rect x="32" y="17" width="43" height="43" rx="21.5" fill="white" />
            <path
                d="M60.5086 49.6113C60.2437 49.6113 59.9786 49.5063 59.7765 49.2964L50.7096 39.8814C50.3051 39.4614 50.3051 38.7805 50.7096 38.3607C51.1141 37.941 51.7695 37.941 52.174 38.3607L61.2407 47.7758C61.6451 48.1958 61.6451 48.8766 61.2407 49.2964C61.0383 49.5063 60.7734 49.6113 60.5086 49.6113Z"
                fill="#3B6BAB"
            />
            <path
                d="M91.9334 62.4512C94.7936 62.4512 97.1109 64.8576 97.1109 67.8275C97.1109 70.7974 94.7936 73.2039 91.9334 73.2039C89.0731 73.2039 86.7559 70.7974 86.7559 67.8275C86.7559 64.8576 89.0731 62.4512 91.9334 62.4512Z"
                fill="#8EEB90"
            />
            <path
                d="M14.2697 67.8271C17.701 67.8271 20.4827 64.9386 20.4827 61.3754C20.4827 57.8123 17.701 54.9238 14.2697 54.9238C10.8383 54.9238 8.05664 57.8123 8.05664 61.3754C8.05664 64.9386 10.8383 67.8271 14.2697 67.8271Z"
                fill="#8EEB90"
            />
            <path
                d="M62.62 77.0381C74.0561 78.8295 82.3919 84.3779 82.3919 90.9456C82.3919 98.9628 69.9742 105.462 54.655 105.462C39.3357 105.462 26.918 98.9628 26.918 90.9456C26.918 84.3779 35.2538 78.8295 46.6899 77.0381L54.655 85.8919L62.62 77.0381Z"
                fill="#FF8200"
                fillOpacity="0.21"
            />
            <path
                d="M41.7423 68.1435C31.0311 63.0532 23.5879 51.8274 23.5879 38.7952C23.5879 20.9801 37.4968 6.53711 54.653 6.53711C71.8092 6.53711 85.7181 20.9801 85.7181 38.7952C85.7181 51.8274 78.2751 63.0532 67.5636 68.1435C65.9725 71.0053 56.2626 88.468 54.653 91.3631C53.0428 88.467 43.3347 71.0076 41.7423 68.1435ZM74.3275 38.7952C74.3275 27.5113 65.5196 18.3651 54.653 18.3651C43.7864 18.3651 34.9784 27.5113 34.9784 38.7952C34.9784 50.079 43.7864 59.2253 54.653 59.2253C65.5196 59.2253 74.3275 50.079 74.3275 38.7952Z"
                fill="#F20305"
            />
            <path
                d="M50.5916 31.1775C52.938 33.6141 53.3191 37.1711 51.4407 39.1194C49.5623 41.07 46.1389 40.6743 43.7904 38.2377C41.4439 35.8012 41.0629 32.2442 42.9413 30.2936C44.8197 28.3453 48.2431 28.7388 50.5916 31.1775Z"
                fill="#B5DBFF"
            />
            <path
                d="M99.6978 54.925C100.556 54.925 101.251 54.2029 101.251 53.3121C101.251 52.4213 100.556 51.6992 99.6978 51.6992C98.8399 51.6992 98.1445 52.4213 98.1445 53.3121C98.1445 54.2029 98.8399 54.925 99.6978 54.925Z"
                fill="#FEE682"
            />
            <path
                d="M92.4497 91.4826C93.3076 91.4826 94.003 90.7605 94.003 89.8697C94.003 88.979 93.3076 88.2568 92.4497 88.2568C91.5919 88.2568 90.8965 88.979 90.8965 89.8697C90.8965 90.7605 91.5919 91.4826 92.4497 91.4826Z"
                fill="#FEE682"
            />
            <path
                d="M89.3423 16.2141C90.2002 16.2141 90.8956 15.492 90.8956 14.6012C90.8956 13.7104 90.2002 12.9883 89.3423 12.9883C88.4845 12.9883 87.7891 13.7104 87.7891 14.6012C87.7891 15.492 88.4845 16.2141 89.3423 16.2141Z"
                fill="#FEE682"
            />
            <path
                d="M25.1411 75.3547C25.999 75.3547 26.6944 74.6326 26.6944 73.7418C26.6944 72.851 25.999 72.1289 25.1411 72.1289C24.2833 72.1289 23.5879 72.851 23.5879 73.7418C23.5879 74.6326 24.2833 75.3547 25.1411 75.3547Z"
                fill="#FEE682"
            />
            <path
                d="M15.8228 19.9797C16.6806 19.9797 17.376 19.2576 17.376 18.3668C17.376 17.476 16.6806 16.7539 15.8228 16.7539C14.9649 16.7539 14.2695 17.476 14.2695 18.3668C14.2695 19.2576 14.9649 19.9797 15.8228 19.9797Z"
                fill="#FEE682"
            />
            <path
                d="M14.7876 101.161C15.6455 101.161 16.3409 100.439 16.3409 99.5485C16.3409 98.6577 15.6455 97.9355 14.7876 97.9355C13.9298 97.9355 13.2344 98.6577 13.2344 99.5485C13.2344 100.439 13.9298 101.161 14.7876 101.161Z"
                fill="#FEE682"
            />
            <path
                d="M68.2925 33.9464C67.8881 33.5266 67.2326 33.5266 66.8281 33.9464L61.162 39.8302C60.647 40.3651 59.8873 40.515 59.5357 40.15L59.1097 39.7081L63.4774 35.1726C63.8819 34.7526 63.8819 34.0717 63.4774 33.6519C63.073 33.2322 62.4175 33.2322 62.013 33.6519L57.6447 38.1881L57.2168 37.7444C57.0173 37.5375 57.014 37.2354 57.0296 37.0737C57.0639 36.7188 57.2456 36.3474 57.5274 36.0543L63.1937 30.1704C63.598 29.7504 63.598 29.0696 63.1937 28.6498C62.7892 28.23 62.1338 28.23 61.7293 28.6498L56.0632 34.5337C55.4399 35.1808 55.0513 36.0066 54.9691 36.8588C54.8801 37.7786 55.1657 38.6556 55.7526 39.2651L56.18 39.7087L48.4127 47.7743C48.0083 48.1943 48.0083 48.8752 48.4127 49.295C48.6151 49.5049 48.8799 49.6098 49.145 49.6098C49.4101 49.6098 49.675 49.5049 49.8773 49.295L57.6453 41.2285L58.0721 41.6713C58.6056 42.2251 59.3199 42.4967 60.0665 42.4967C60.9587 42.4967 61.8966 42.1087 62.6267 41.3509L68.2927 35.467C68.6972 35.047 68.6972 34.3661 68.2925 33.9464Z"
                fill="#00467D"
            />
        </svg>
    )
}

export default FindNearbyIcon
