import React from 'react'
import { useTheme } from '@emotion/react'

const PartialSvg = () => {
    const theme = useTheme()
    return (
        <svg
            width="20"
            height="20"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_27_1035)">
                <path
                    d="M24.0505 8.59454V11.7888C24.0501 11.9299 23.9939 12.065 23.8942 12.1648C23.7944 12.2646 23.6592 12.3208 23.5182 12.3212H3.81915C3.04422 12.3216 2.2956 12.0401 1.7129 11.5292C1.13021 11.0184 0.753212 10.313 0.65223 9.54469C0.551248 8.77636 0.733172 7.99754 1.16407 7.35346C1.59498 6.70938 2.24544 6.24402 2.99415 6.04416C3.26257 5.96841 3.54025 5.93054 3.81915 5.93166H21.3885C22.0946 5.9318 22.7718 6.21242 23.271 6.7118C23.7702 7.21118 24.0506 7.88842 24.0505 8.59454Z"
                    fill="#FFD0AE"
                />
                <path
                    d="M24.0513 8.59329V11.7875C24.0509 11.9286 23.9947 12.0638 23.8949 12.1636C23.7952 12.2633 23.66 12.3195 23.5189 12.3199H3.81993C3.26092 12.3211 2.71158 12.1742 2.22781 11.894C2.22268 11.729 2.22268 11.5639 2.22268 11.394C2.21959 9.58273 2.47969 7.78057 2.99481 6.04404C3.26322 5.96829 3.54091 5.93042 3.81981 5.93154H21.3893C22.0952 5.93168 22.7722 6.21214 23.2714 6.71128C23.7706 7.21042 24.0511 7.88737 24.0513 8.59329Z"
                    fill="#FFE1CC"
                />
                <path
                    d="M24.0522 8.59389V11.7881C24.0518 11.9292 23.9956 12.0644 23.8959 12.1641C23.7961 12.2639 23.6609 12.3201 23.5199 12.3205H12.2647C11.4789 10.2884 11.2243 8.08953 11.5249 5.93164H21.3902C22.0962 5.93177 22.7733 6.21231 23.2725 6.71156C23.7717 7.2108 24.0522 7.88788 24.0522 8.59389Z"
                    fill="white"
                />
                <path
                    d="M25.4393 11.34C25.4401 11.3579 25.4384 11.3758 25.4341 11.3932C25.3794 11.7394 25.2236 12.0616 24.9864 12.3196C24.9266 12.3858 24.8625 12.448 24.7948 12.506L16.5799 19.4006L12.7364 22.6271C12.6417 22.7084 12.5204 22.752 12.3956 22.7495C12.3187 22.7498 12.2426 22.7332 12.1728 22.701C12.1029 22.6688 12.0409 22.6218 11.9911 22.5631L3.08887 11.9629C2.99972 11.8538 2.95587 11.7147 2.96637 11.5742C2.97171 11.5044 2.99094 11.4364 3.02295 11.3742C3.05495 11.3119 3.09909 11.2567 3.15275 11.2117L5.80963 8.98148L13.5614 2.47548L15.2114 1.09111C15.5489 0.801735 15.981 0.646402 16.4255 0.654606C16.6451 0.656196 16.8617 0.705593 17.0603 0.799365C17.2589 0.893136 17.4347 1.02903 17.5755 1.19761L25.104 10.1634C25.2426 10.3252 25.3427 10.5164 25.3969 10.7225C25.454 10.9231 25.4684 11.1334 25.4393 11.34Z"
                    fill="#C6590B"
                />
                <path
                    d="M25.4402 11.34C25.441 11.3579 25.4393 11.3758 25.435 11.3932C25.3803 11.7394 25.2245 12.0616 24.9873 12.3196C24.9275 12.3858 24.8635 12.448 24.7957 12.506L16.5808 19.4006C14.1327 18.5575 11.8994 17.1875 10.0385 15.3873C8.17752 13.587 6.73428 11.4003 5.81055 8.98148L13.5623 2.47548L15.2123 1.09111C15.5498 0.801735 15.9819 0.646402 16.4264 0.654606C16.646 0.656196 16.8627 0.705593 17.0613 0.799365C17.2599 0.893136 17.4357 1.02903 17.5764 1.19761L25.1049 10.1634C25.2435 10.3252 25.3436 10.5164 25.3978 10.7225C25.4549 10.9231 25.4693 11.1334 25.4402 11.34Z"
                    fill="#FF7918"
                />
                <path
                    d="M25.4404 11.34C25.3712 11.3454 25.3021 11.3454 25.233 11.3454C22.5843 11.3511 20.0072 10.4859 17.8984 8.88321C15.7896 7.28049 14.2661 5.02903 13.5625 2.47548L15.2125 1.09111C15.55 0.801735 15.9821 0.646402 16.4266 0.654606C16.6462 0.656196 16.8629 0.705593 17.0615 0.799365C17.2601 0.893136 17.4359 1.02903 17.5766 1.19761L25.1051 10.1634C25.2437 10.3252 25.3438 10.5164 25.398 10.7225C25.4551 10.9231 25.4695 11.1334 25.4404 11.34Z"
                    fill="#FFC399"
                />
                <path
                    d="M10.6852 20.7125C10.6075 20.7126 10.5306 20.6957 10.4602 20.6629C10.3897 20.63 10.3273 20.5821 10.2775 20.5225L4.80058 14.0005C4.70968 13.8922 4.66552 13.7522 4.6778 13.6113C4.69008 13.4704 4.7578 13.3402 4.86608 13.2492L14.1411 5.46547C14.2493 5.37487 14.389 5.33088 14.5296 5.34316C14.6702 5.35544 14.8002 5.42298 14.8911 5.53098C15.3026 6.02135 16.5643 5.81422 17.1697 5.30597C17.2779 5.21537 17.4176 5.17138 17.5582 5.18366C17.6988 5.19594 17.8288 5.26348 17.9197 5.37147L21.0493 9.09923C21.1402 9.20729 21.1844 9.34701 21.1722 9.48766C21.16 9.62831 21.0925 9.75836 20.9845 9.84923C20.3791 10.3571 19.9563 11.5625 20.3685 12.0539C20.4135 12.1074 20.4474 12.1693 20.4685 12.236C20.4895 12.3027 20.4972 12.3729 20.491 12.4426C20.4849 12.5122 20.4651 12.58 20.4327 12.642C20.4003 12.704 20.356 12.759 20.3023 12.8039L11.0273 20.5877C10.9317 20.6684 10.8106 20.7127 10.6855 20.7127M5.95858 13.7231L10.7502 19.4305L19.2772 12.2755C18.9141 11.363 19.3257 10.1697 19.9272 9.4196L17.4111 6.4216C16.5678 6.88485 15.321 7.08285 14.4845 6.5686L5.95858 13.7231Z"
                    fill="white"
                />
                <path
                    d="M27.2457 13.9181V26.6959C27.2455 27.402 26.9649 28.0791 26.4656 28.5784C25.9664 29.0777 25.2892 29.3582 24.5832 29.3584H3.28778C2.58169 29.3582 1.90457 29.0777 1.40529 28.5784C0.906009 28.0791 0.625443 27.402 0.625277 26.6959V9.65889C0.623257 9.60785 0.632297 9.55698 0.651777 9.50977C0.710183 10.002 0.883165 10.4737 1.15684 10.887C1.43051 11.3003 1.79726 11.6437 2.22765 11.8896C2.70996 12.1739 3.25996 12.3228 3.81978 12.3209H24.9878C25.2249 12.0629 25.3805 11.7406 25.4352 11.3945C25.9629 11.5731 26.4214 11.9126 26.7462 12.3652C27.0709 12.8179 27.2456 13.361 27.2457 13.9181Z"
                    fill="#FF7918"
                />
                <path
                    d="M27.2446 13.9182V26.6959C27.2444 27.402 26.9638 28.0791 26.4646 28.5784C25.9653 29.0777 25.2882 29.3582 24.5821 29.3584H15.0153C7.71056 26.7972 2.43431 19.9663 2.22656 11.8897C2.70887 12.1739 3.25887 12.3228 3.81869 12.3209H24.9867C25.2238 12.0629 25.3794 11.7406 25.4341 11.3945C25.9618 11.5731 26.4203 11.9126 26.7451 12.3652C27.0698 12.8179 27.2445 13.361 27.2446 13.9182Z"
                    fill="#FF8F3D"
                />
                <path
                    d="M27.2457 13.9182V20.3657C26.3363 20.565 25.408 20.6649 24.477 20.6638C21.8368 20.6696 19.2567 19.8756 17.0765 18.3864C14.8964 16.8971 13.2184 14.7825 12.2637 12.3209H24.9878C25.2249 12.0629 25.3805 11.7406 25.4352 11.3945C25.9629 11.5731 26.4214 11.9126 26.7462 12.3652C27.0709 12.8179 27.2456 13.361 27.2457 13.9182Z"
                    fill="#F8AF7B"
                />
                <path
                    d="M29.3744 18.7082V22.9676C29.3732 23.3908 29.2045 23.7963 28.9052 24.0956C28.606 24.3948 28.2004 24.5635 27.7772 24.5647H23.5179C22.5299 24.5636 21.5825 24.1706 20.8839 23.4719C20.1852 22.7732 19.7922 21.8259 19.7911 20.8378C19.7899 20.6361 19.8077 20.4347 19.8444 20.2363C19.9871 19.3646 20.4347 18.5718 21.1075 17.9995C21.7803 17.4271 22.6346 17.1123 23.5179 17.1113H27.7772C28.1408 17.1129 28.4933 17.2367 28.7781 17.4627C28.9639 17.6128 29.1139 17.8026 29.217 18.018C29.3202 18.2335 29.374 18.4693 29.3744 18.7082Z"
                    fill="#FF7918"
                />
                <path
                    d="M28.7781 17.4625C28.0686 20.0974 26.7522 22.5297 24.9342 24.5647H23.5179C22.5299 24.5636 21.5825 24.1706 20.8839 23.4719C20.1852 22.7732 19.7922 21.8259 19.7911 20.8378C19.7899 20.6361 19.8077 20.4347 19.8444 20.2363C19.9871 19.3646 20.4347 18.5718 21.1075 17.9995C21.7803 17.4271 22.6346 17.1123 23.5179 17.1113H27.7772C28.1408 17.1128 28.4933 17.2365 28.7781 17.4625Z"
                    fill="#F8AA71"
                />
                <path
                    d="M23.7996 17.1113C22.7156 18.4198 21.3676 19.4847 19.8438 20.2363C19.9864 19.3646 20.434 18.5718 21.1068 17.9995C21.7796 17.4271 22.6339 17.1123 23.5173 17.1113H23.7996Z"
                    fill="#FFC399"
                />
                <path
                    d="M25.6473 20.8401C25.6474 21.0508 25.5849 21.2567 25.4679 21.4318C25.3509 21.6069 25.1846 21.7434 24.99 21.8241C24.7954 21.9047 24.5813 21.9258 24.3747 21.8847C24.1682 21.8436 23.9784 21.7422 23.8295 21.5932C23.6805 21.4443 23.5791 21.2546 23.538 21.048C23.4969 20.8414 23.518 20.6273 23.5987 20.4327C23.6793 20.2381 23.8158 20.0718 23.9909 19.9548C24.1661 19.8378 24.372 19.7754 24.5826 19.7754C24.8649 19.7755 25.1357 19.8877 25.3354 20.0874C25.535 20.287 25.6472 20.5578 25.6473 20.8401Z"
                    fill="white"
                />
                <path
                    d="M4.88394 24.0334C4.81402 24.0334 4.74479 24.0197 4.68019 23.9929C4.61559 23.9662 4.55689 23.927 4.50745 23.8775C4.45802 23.8281 4.4188 23.7694 4.39205 23.7048C4.36531 23.6402 4.35155 23.5709 4.35156 23.501V18.1769C4.35156 18.0357 4.40765 17.9003 4.50749 17.8005C4.60733 17.7006 4.74274 17.6445 4.88394 17.6445C5.02513 17.6445 5.16054 17.7006 5.26038 17.8005C5.36022 17.9003 5.41631 18.0357 5.41631 18.1769V23.501C5.41633 23.5709 5.40257 23.6402 5.37582 23.7048C5.34907 23.7694 5.30986 23.8281 5.26042 23.8775C5.21098 23.927 5.15229 23.9662 5.08769 23.9929C5.02309 24.0197 4.95385 24.0334 4.88394 24.0334Z"
                    fill="white"
                />
            </g>
            <defs>
                <clipPath id="clip0_27_1035">
                    <rect width="30" height="30" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default PartialSvg
