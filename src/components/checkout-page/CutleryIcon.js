import React from 'react'
import { useTheme } from '@mui/material'

const CutleryIcon = () => {
    const theme = useTheme()
    const primaryColor = theme.palette.primary.main
    return (
        <svg
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M2 18V7.725C1.45 7.725 0.979167 7.52917 0.5875 7.1375C0.195833 6.74583 0 6.275 0 5.725V0.575C0 0.425 0.0583333 0.291667 0.175 0.175C0.291667 0.0583333 0.425 0 0.575 0C0.725 0 0.8625 0.0583333 0.9875 0.175C1.1125 0.291667 1.175 0.425 1.175 0.575V4.125H2.175V0.575C2.175 0.425 2.23333 0.291667 2.35 0.175C2.46667 0.0583333 2.6 0 2.75 0C2.9 0 3.03333 0.0583333 3.15 0.175C3.26667 0.291667 3.325 0.425 3.325 0.575V4.125H4.325V0.575C4.325 0.425 4.3875 0.291667 4.5125 0.175C4.6375 0.0583333 4.775 0 4.925 0C5.075 0 5.20833 0.0583333 5.325 0.175C5.44167 0.291667 5.5 0.425 5.5 0.575V5.725C5.5 6.275 5.30417 6.74583 4.9125 7.1375C4.52083 7.52917 4.05 7.725 3.5 7.725V18H2ZM9 18V7.675C8.31667 7.29167 7.8 6.775 7.45 6.125C7.1 5.475 6.925 4.725 6.925 3.875C6.925 2.875 7.17917 1.97917 7.6875 1.1875C8.19583 0.395833 8.89167 0 9.775 0C10.6583 0 11.3542 0.395833 11.8625 1.1875C12.3708 1.97917 12.625 2.875 12.625 3.875C12.625 4.725 12.4417 5.475 12.075 6.125C11.7083 6.775 11.1833 7.29167 10.5 7.675V18H9ZM14.35 18V0C15.3167 0.0833333 16.1667 0.4625 16.9 1.1375C17.6333 1.8125 18 2.65 18 3.65V9.75H15.85V18H14.35Z"
                fill={primaryColor}
            />
        </svg>
    )
}

export default CutleryIcon
