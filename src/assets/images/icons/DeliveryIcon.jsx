import { useTheme } from '@mui/material'
import React from 'react'

const DeliveryIcon = (props) => {
    const theme = useTheme()
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.8346 12.2901H1.66797C1.3263 12.2901 1.04297 12.0068 1.04297 11.6651V6.34845C1.04297 6.08178 1.20963 5.84843 1.45129 5.75676C1.70129 5.6651 1.97631 5.74012 2.14297 5.94012C2.65131 6.54846 3.44297 6.89011 4.22631 6.86511C4.89297 6.84845 5.50962 6.59847 5.97629 6.1568C6.19295 5.97347 6.36796 5.75678 6.50129 5.51512C6.75962 5.07345 6.88464 4.59011 6.8763 4.09844C6.85964 3.33177 6.52632 2.63178 5.95966 2.12344C5.75966 1.94844 5.69297 1.67343 5.78464 1.43176C5.8763 1.1901 6.10965 1.02344 6.36798 1.02344H12.5013C12.843 1.02344 13.1263 1.30677 13.1263 1.64844V9.98177C13.1263 11.2651 12.1013 12.2901 10.8346 12.2901ZM2.29297 11.0401H10.8346C11.4096 11.0401 11.8763 10.5735 11.8763 9.99845V2.29012H7.65964C7.9513 2.83179 8.10964 3.44013 8.1263 4.07347C8.14297 4.79847 7.95962 5.51512 7.59296 6.14012C7.39296 6.49845 7.11796 6.84013 6.81796 7.09013C6.1513 7.72346 5.23462 8.09845 4.25962 8.12345C3.55962 8.14845 2.88464 7.97346 2.30131 7.65679V11.0401H2.29297Z" fill={theme.palette.text.secondary } />
            <path d="M15.8346 17.293H15.0013C14.6596 17.293 14.3763 17.0096 14.3763 16.668C14.3763 16.093 13.9096 15.6263 13.3346 15.6263C12.7596 15.6263 12.293 16.093 12.293 16.668C12.293 17.0096 12.0096 17.293 11.668 17.293H8.33464C7.99297 17.293 7.70964 17.0096 7.70964 16.668C7.70964 16.093 7.24297 15.6263 6.66797 15.6263C6.09297 15.6263 5.6263 16.093 5.6263 16.668C5.6263 17.0096 5.34297 17.293 5.0013 17.293H4.16797C2.44297 17.293 1.04297 15.893 1.04297 14.168V11.668C1.04297 11.3263 1.3263 11.043 1.66797 11.043H10.8346C11.4096 11.043 11.8763 10.5763 11.8763 10.0013V4.16797C11.8763 3.8263 12.1596 3.54297 12.5013 3.54297H14.0347C14.8597 3.54297 15.618 3.98465 16.0263 4.70131L17.4513 7.19297C17.5596 7.38464 17.5596 7.62631 17.4513 7.81797C17.343 8.00964 17.1346 8.1263 16.9096 8.1263H15.8346C15.718 8.1263 15.6263 8.21797 15.6263 8.33463V10.8346C15.6263 10.9513 15.718 11.043 15.8346 11.043H18.3346C18.6763 11.043 18.9596 11.3263 18.9596 11.668V14.168C18.9596 15.893 17.5596 17.293 15.8346 17.293ZM15.543 16.043H15.8346C16.868 16.043 17.7096 15.2013 17.7096 14.168V12.293H15.8346C15.0346 12.293 14.3763 11.6346 14.3763 10.8346V8.33463C14.3763 7.53464 15.0263 6.8763 15.8346 6.8763L14.943 5.31797C14.7596 4.99297 14.4097 4.79297 14.0347 4.79297H13.1263V10.0013C13.1263 11.268 12.1013 12.293 10.8346 12.293H2.29297V14.168C2.29297 15.2013 3.13464 16.043 4.16797 16.043H4.45962C4.73462 15.0846 5.61797 14.3763 6.66797 14.3763C7.71797 14.3763 8.60132 15.0846 8.87632 16.043H11.1346C11.4096 15.0846 12.293 14.3763 13.343 14.3763C14.393 14.3763 15.268 15.0846 15.543 16.043Z" fill="#9B9B9B" />
            <path d="M6.66667 18.9583C5.4 18.9583 4.375 17.9333 4.375 16.6667C4.375 15.4 5.4 14.375 6.66667 14.375C7.93333 14.375 8.95833 15.4 8.95833 16.6667C8.95833 17.9333 7.93333 18.9583 6.66667 18.9583ZM6.66667 15.625C6.09167 15.625 5.625 16.0917 5.625 16.6667C5.625 17.2417 6.09167 17.7083 6.66667 17.7083C7.24167 17.7083 7.70833 17.2417 7.70833 16.6667C7.70833 16.0917 7.24167 15.625 6.66667 15.625Z" fill="#9B9B9B" />
            <path d="M13.3346 18.9583C12.068 18.9583 11.043 17.9333 11.043 16.6667C11.043 15.4 12.068 14.375 13.3346 14.375C14.6013 14.375 15.6263 15.4 15.6263 16.6667C15.6263 17.9333 14.6013 18.9583 13.3346 18.9583ZM13.3346 15.625C12.7596 15.625 12.293 16.0917 12.293 16.6667C12.293 17.2417 12.7596 17.7083 13.3346 17.7083C13.9096 17.7083 14.3763 17.2417 14.3763 16.6667C14.3763 16.0917 13.9096 15.625 13.3346 15.625Z" fill="#9B9B9B" />
            <path d="M18.3333 12.2917H15.8333C15.0333 12.2917 14.375 11.6333 14.375 10.8333V8.33333C14.375 7.53333 15.0333 6.875 15.8333 6.875H16.9083C17.1333 6.875 17.3417 6.99167 17.45 7.19167L18.875 9.69167C18.925 9.78334 18.9583 9.89167 18.9583 10V11.6667C18.9583 12.0083 18.675 12.2917 18.3333 12.2917ZM15.8333 8.125C15.7167 8.125 15.625 8.21667 15.625 8.33333V10.8333C15.625 10.95 15.7167 11.0417 15.8333 11.0417H17.7083V10.1667L16.5416 8.125H15.8333Z" fill="#9B9B9B" />
            <path d="M4.15801 8.1237C3.00801 8.1237 1.92468 7.62368 1.19135 6.74868C1.07468 6.62368 0.949696 6.44868 0.841363 6.28201C0.449696 5.69035 0.233029 4.99035 0.216363 4.25702C0.183029 3.04035 0.699706 1.89868 1.63304 1.12368C2.34137 0.540344 3.19133 0.223698 4.09133 0.207031C5.07467 0.215365 6.05804 0.540367 6.79137 1.1987C7.62471 1.93203 8.10803 2.95704 8.13303 4.07371C8.1497 4.79871 7.96635 5.51536 7.59968 6.14036C7.39968 6.49869 7.12469 6.84037 6.82469 7.09037C6.15802 7.72371 5.24135 8.0987 4.26635 8.1237C4.22468 8.1237 4.19135 8.1237 4.15801 8.1237ZM4.15801 1.45703C4.14135 1.45703 4.12468 1.45703 4.10801 1.45703C3.49968 1.46536 2.91633 1.69037 2.42467 2.09037C1.79133 2.61537 1.44135 3.3987 1.45802 4.2237C1.47469 4.7237 1.61636 5.19872 1.88303 5.59038C1.95803 5.70705 2.03301 5.81534 2.12468 5.91534C2.65801 6.54868 3.44969 6.88202 4.22469 6.86536C4.89136 6.84869 5.50801 6.59871 5.97467 6.15704C6.19134 5.97371 6.36634 5.75703 6.49968 5.51536C6.75801 5.07369 6.88302 4.59035 6.87469 4.09868C6.85802 3.33202 6.52471 2.63202 5.95804 2.12369C5.45804 1.69869 4.82468 1.45703 4.15801 1.45703Z" fill="#9B9B9B" />
            <path d="M3.70807 5.62539C3.54973 5.62539 3.39972 5.56703 3.27472 5.45037L2.43304 4.6504C2.18304 4.40873 2.17475 4.01707 2.41641 3.76707C2.65808 3.51707 3.04974 3.50872 3.29974 3.75039L3.70807 4.14203L5.01641 2.8754C5.26641 2.63373 5.65807 2.64203 5.89974 2.89203C6.14141 3.14203 6.13306 3.53374 5.88306 3.77541L4.14137 5.45871C4.01637 5.56704 3.85807 5.62539 3.70807 5.62539Z" fill="#9B9B9B" />
        </svg>

    )
}

export default DeliveryIcon