export const settings = {
    speed: 500,
    slidesToShow: 4.7,
    slidesToScroll: 1,
    initialSlide: 0,

    responsive: [
        {
            breakpoint: 3600,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 3200,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        }
        ,
        {
            breakpoint: 2800,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        }
        ,
        {
            breakpoint: 2400,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 2000,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 1600,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 1340,
            settings: {
                slidesToShow:4.5,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 1075,
            settings: {
                slidesToShow: 4,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 999,
            settings: {
                slidesToShow: 3.5,
                slidesToScroll: 1,
                infinite: false,
                // dots: true
            },
        },
        {
            breakpoint: 800,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                // initialSlide: 2
                infinite: false,
            },
        },
        {
            breakpoint: 670,
            settings: {
                slidesToShow: 2.5,
                slidesToScroll: 2,
                initialSlide: 0,
                infinite: false,
            },
        },
        {
            breakpoint: 540,
            settings: {
                slidesToShow: 2,
                slidesToScroll: 1,
                infinite: false,
            },
        },
        {
            breakpoint: 400,
            settings: {
                slidesToShow: 1.7,
                slidesToScroll: 2,

                // dots: true
                initialSlide: 0,
                infinite: false,
            },
        },
    ],
}
