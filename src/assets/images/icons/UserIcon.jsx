import React from 'react'

const UserIcon = (props) => {
    const { size = "26" } = props;
    return (
        <svg width={size} height={size} viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20.8528 14.1375C22.2747 15.0191 23.1562 16.6075 23.1562 18.2772C23.1562 20.085 22.1203 21.7872 20.5115 22.6159C19.8372 22.9613 19.0693 23.1485 18.2934 23.1485C17.6881 23.1485 17.0909 23.0348 16.5343 22.8231C16.124 22.6688 15.7381 22.4616 15.3887 22.202C14.0359 21.2026 13.2843 19.5406 13.4225 17.8628C13.5647 16.2098 14.5681 14.7063 16.0509 13.9429C16.7293 13.5935 17.4976 13.4103 18.2772 13.4103C18.4356 13.4103 18.5981 13.4188 18.7525 13.4388C19.504 13.5038 20.2231 13.7434 20.8528 14.1375ZM20.5806 16.5547C20.739 16.3963 20.739 16.1403 20.5806 15.9819C20.4218 15.8234 20.1622 15.8234 20.0037 15.9819L18.2812 17.7044L16.5587 15.9819C16.4003 15.8234 16.1407 15.8234 15.9818 15.9819C15.8234 16.1407 15.8234 16.3967 15.9818 16.5547L17.7084 18.2813L15.9818 20.0038C15.8234 20.1622 15.8234 20.4181 15.9818 20.5766C16.0631 20.6574 16.1647 20.6944 16.2703 20.6944C16.3759 20.6944 16.4775 20.6574 16.5587 20.5766L18.2812 18.8541L20.0037 20.5766C20.085 20.6574 20.1865 20.6944 20.2922 20.6944C20.3978 20.6944 20.4993 20.6574 20.5806 20.5766C20.739 20.4177 20.739 20.1618 20.5806 20.0038L18.8581 18.2813L20.5806 16.5547Z" fill="#FF6D6D" />
            <path d="M16.8595 7.51156C16.8595 8.77459 16.3476 9.95718 15.4132 10.8428C15.2264 11.0212 15.0232 11.1841 14.812 11.3258C14.0157 11.8702 13.0692 12.1749 12.1185 12.1749C11.3995 12.1749 10.7048 12.0043 10.0589 11.6716C9.87604 11.5781 9.70541 11.4725 9.53885 11.3624C9.53479 11.3543 9.53479 11.3543 9.53073 11.3543C8.3526 10.5296 7.58479 9.16053 7.51979 7.7224C7.44666 6.03687 8.2876 4.43584 9.7176 3.54656C10.4529 3.0875 11.306 2.84781 12.1917 2.84781C13.0042 2.84781 13.7964 3.055 14.491 3.4454C15.9495 4.27781 16.8595 5.83375 16.8595 7.51156Z" fill="#334257" />
            <path d="M15.1084 12.1022C15.7946 12.2525 16.473 12.4597 17.1275 12.7156C16.6197 12.8172 16.1322 12.9878 15.6772 13.2194C13.9506 14.1131 12.7766 15.8641 12.6141 17.7937C12.4475 19.7519 13.325 21.6897 14.9057 22.8556C14.9663 22.9003 15.0276 22.9454 15.0881 22.9856C11.2328 23.4325 7.30437 22.9771 3.64447 21.6491C3.16469 21.4744 2.84375 21.0153 2.84375 20.5034V17.6759C2.84375 16.1444 3.61156 14.7392 4.90344 13.91C6.22781 13.0609 7.67406 12.4556 9.20969 12.1144C9.36406 12.2159 9.5225 12.309 9.68906 12.3943C10.4528 12.7888 11.2694 12.9874 12.1184 12.9874C13.1666 12.9878 14.2147 12.6709 15.1084 12.1022Z" fill="#334257" />
        </svg>

    )
}

export default UserIcon