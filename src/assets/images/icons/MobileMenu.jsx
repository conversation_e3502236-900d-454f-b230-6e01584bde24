import { useTheme } from '@mui/material'
import React from 'react'

const MobileMenu = () => {
    const theme = useTheme()
    return (
        <svg
            width="26"
            height="26"
            viewBox="0 0 26 26"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M8.875 12.2502C8.77554 12.2502 8.68016 12.2107 8.60984 12.1404C8.53951 12.0701 8.5 11.9747 8.5 11.8752V8.87598C8.5 8.77652 8.53951 8.68114 8.60984 8.61081C8.68016 8.54049 8.77554 8.50098 8.875 8.50098H11.875C11.9745 8.50098 12.0698 8.54049 12.1402 8.61081C12.2105 8.68114 12.25 8.77652 12.25 8.87598V11.8752C12.25 11.9747 12.2105 12.0701 12.1402 12.1404C12.0698 12.2107 11.9745 12.2502 11.875 12.2502H8.875ZM14.125 12.2502C14.0255 12.2502 13.9302 12.2107 13.8598 12.1404C13.7895 12.0701 13.75 11.9747 13.75 11.8752V8.87598C13.75 8.77652 13.7895 8.68114 13.8598 8.61081C13.9302 8.54049 14.0255 8.50098 14.125 8.50098H17.1242C17.2237 8.50098 17.3191 8.54049 17.3894 8.61081C17.4597 8.68114 17.4992 8.77652 17.4992 8.87598V11.8752C17.4992 11.9747 17.4597 12.0701 17.3894 12.1404C17.3191 12.2107 17.2237 12.2502 17.1242 12.2502H14.125ZM8.875 17.5002C8.77554 17.5002 8.68016 17.4607 8.60984 17.3904C8.53951 17.3201 8.5 17.2247 8.5 17.1252V14.1252C8.5 14.0258 8.53951 13.9304 8.60984 13.8601C8.68016 13.7897 8.77554 13.7502 8.875 13.7502H11.875C11.9745 13.7502 12.0698 13.7897 12.1402 13.8601C12.2105 13.9304 12.25 14.0258 12.25 14.1252V17.1252C12.25 17.2247 12.2105 17.3201 12.1402 17.3904C12.0698 17.4607 11.9745 17.5002 11.875 17.5002H8.875ZM14.125 17.5002C14.0255 17.5002 13.9302 17.4607 13.8598 17.3904C13.7895 17.3201 13.75 17.2247 13.75 17.1252V14.1252C13.75 14.0258 13.7895 13.9304 13.8598 13.8601C13.9302 13.7897 14.0255 13.7502 14.125 13.7502H17.1242C17.2237 13.7502 17.3191 13.7897 17.3894 13.8601C17.4597 13.9304 17.4992 14.0258 17.4992 14.1252V17.1252C17.4992 17.2247 17.4597 17.3201 17.3894 17.3904C17.3191 17.4607 17.2237 17.5002 17.1242 17.5002H14.125Z"
                fill={theme.palette.primary.main}
            />
            <path
                opacity="0.4"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M24.6459 13.0621L24.6459 12.9386C24.6459 10.4372 24.6459 8.47741 24.44 6.94774C24.2299 5.38341 23.79 4.14841 22.8215 3.17883C21.852 2.20924 20.617 1.77049 19.0515 1.56033C17.523 1.35449 15.5632 1.35449 13.0618 1.35449L12.9383 1.35449C10.4369 1.35449 8.47713 1.35449 6.94746 1.56032C5.38313 1.77049 4.14812 2.21032 3.17854 3.17882C2.20896 4.14841 1.77021 5.38341 1.56004 6.94882C1.35421 8.47741 1.35421 10.4372 1.35421 12.9386L1.35421 13.0621C1.35421 15.5635 1.35421 17.5232 1.56004 19.0529C1.77021 20.6172 2.21004 21.8522 3.17854 22.8218C4.14812 23.7914 5.38312 24.2302 6.94854 24.4403C8.47712 24.6462 10.4369 24.6462 12.9383 24.6462L13.0618 24.6462C15.5632 24.6462 17.523 24.6462 19.0526 24.4403C20.617 24.2302 21.852 23.7903 22.8215 22.8218C23.7911 21.8522 24.2299 20.6172 24.44 19.0518C24.6459 17.5232 24.6459 15.5635 24.6459 13.0621ZM21.6721 4.32824C22.2896 4.94574 22.6439 5.77991 22.8302 7.16549C23.0187 8.57382 23.0209 10.4242 23.0209 13.0003C23.0209 15.5765 23.0187 17.4268 22.8302 18.8352C22.6439 20.2207 22.2885 21.056 21.6721 21.6735C21.0546 22.2899 20.2205 22.6442 18.8349 22.8305C17.4265 23.019 15.5762 23.0212 13 23.0212C10.4239 23.0212 8.57354 23.019 7.16521 22.8305C5.77962 22.6442 4.94437 22.2888 4.32687 21.6724C3.71046 21.0549 3.35621 20.2207 3.16988 18.8352C2.98138 17.4268 2.97921 15.5765 2.97921 13.0003C2.97921 10.4242 2.98138 8.57382 3.16988 7.16549C3.35621 5.77991 3.71154 4.94466 4.32796 4.32716C4.94546 3.71074 5.77962 3.35649 7.16521 3.17016C8.57354 2.98166 10.4239 2.97949 13 2.97949C15.5762 2.97949 17.4265 2.98166 18.8349 3.17016C20.2205 3.35649 21.0546 3.71182 21.6721 4.32824Z"
                fill={theme.palette.primary.main}
            />
        </svg>
    )
}

export default MobileMenu
