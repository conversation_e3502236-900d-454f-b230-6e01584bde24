import { useQuery } from 'react-query'
import MainApi from '../../../api/MainApi'
import { onErrorResponse } from '@/components/ErrorResponse'

const getGuest = async () => {
    // Get FCM token from localStorage if available
    let fcmToken = null
    if (typeof window !== 'undefined') {
        fcmToken = localStorage.getItem('fcm_token')
    }
    
    const requestData = {}
    if (fcmToken) {
        requestData.fcm_token = fcmToken
    }
    
    const { data } = await MainApi.post('api/v1/auth/guest/request', requestData)
    return data
}
export default function useGetGuest() {
    return useQuery('guest', getGuest, {
        enabled: false,
        onError: onErrorResponse,
    })
}
