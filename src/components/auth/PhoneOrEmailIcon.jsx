import React from 'react'

const PhoneOrEmailIcon = () => {
    return (
        <>
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 18 18"
                fill="none"
            >
                <g clipPath="url(#clip0_6975_79560)">
                    <path
                        d="M12.6248 5.83388C13.0152 5.83388 13.3977 5.72418 13.7287 5.51729L18.0007 2.84731C18.0007 2.51741 17.7332 2.25 17.4033 2.25H7.84634C7.51647 2.25 7.24902 2.51741 7.24902 2.84731L11.521 5.51729C11.852 5.72418 12.2345 5.83388 12.6248 5.83388Z"
                        fill="#CBD0D4"
                    />
                    <path
                        d="M12.6248 6.4309C12.2345 6.4309 11.852 6.3212 11.521 6.11431L7.24902 3.44434V8.22284C7.24902 8.88261 7.78389 9.41747 8.44365 9.41747H16.806C17.4658 9.41747 18.0007 8.88261 18.0007 8.22284V3.44434L13.7287 6.11431C13.3977 6.3212 13.0152 6.4309 12.6248 6.4309Z"
                        fill="#CBD0D4"
                    />
                    <path
                        d="M10.1543 11.6479C9.1698 11.6479 8.32549 12.244 7.95968 13.0945C5.76722 12.3896 4.03679 10.6591 3.33192 8.46671C4.18236 8.10086 4.77851 7.25655 4.77851 6.27206C4.77851 4.9525 3.70882 3.88281 2.38925 3.88281C1.06969 3.88281 0 4.9525 0 6.27206C0 11.8802 4.54624 16.4264 10.1543 16.4264C11.4739 16.4264 12.5436 15.3567 12.5436 14.0371C12.5436 12.7176 11.4739 11.6479 10.1543 11.6479Z"
                        fill="#CBD0D4"
                    />
                </g>
                <defs>
                    <clipPath id="clip0_6975_79560">
                        <rect width="18" height="18" fill="white" />
                    </clipPath>
                </defs>
            </svg>
        </>
    )
}

export default PhoneOrEmailIcon
