import React from 'react'
import { useTheme } from '@mui/styles'

const CouponStartSvg = () => {
    const theme = useTheme()
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="22"
            height="20"
            viewBox="0 0 22 20"
            fill="none"
        >
            <path
                d="M7.53239 8.34647C7.53553 8.60971 7.64661 8.86113 7.84155 9.04621C8.03649 9.23129 8.29955 9.33508 8.57366 9.33508C8.84778 9.33508 9.11084 9.23129 9.30577 9.04621C9.50071 8.86113 9.6118 8.60971 9.61494 8.34647C9.6118 8.08324 9.50071 7.83182 9.30577 7.64674C9.11084 7.46166 8.84778 7.35787 8.57366 7.35787C8.29955 7.35787 8.03649 7.46166 7.84155 7.64674C7.64661 7.83182 7.53553 8.08324 7.53239 8.34647ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM7.53239 8.34647C7.53553 8.60971 7.64661 8.86113 7.84155 9.04621C8.03649 9.23129 8.29955 9.33508 8.57366 9.33508C8.84778 9.33508 9.11084 9.23129 9.30577 9.04621C9.50071 8.86113 9.6118 8.60971 9.61494 8.34647C9.6118 8.08324 9.50071 7.83182 9.30577 7.64674C9.11084 7.46166 8.84778 7.35787 8.57366 7.35787C8.29955 7.35787 8.03649 7.46166 7.84155 7.64674C7.64661 7.83182 7.53553 8.08324 7.53239 8.34647ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM10.8254 4.19647C9.22335 4.19817 7.6874 4.81011 6.55457 5.89804C5.42173 6.98597 4.78453 8.46103 4.78277 9.9996C5.11467 17.6996 16.5362 17.6965 16.8681 9.9996C16.8663 8.46103 16.2291 6.98597 15.0963 5.89804C13.9634 4.81011 12.4275 4.19817 10.8254 4.19647ZM6.88159 8.34647C6.95318 6.20272 10.1941 6.20272 10.2657 8.34647C10.1941 10.4933 6.94992 10.4933 6.88159 8.34647ZM8.9316 13.3183C8.88285 13.3856 8.8086 13.4319 8.72477 13.4472C8.64093 13.4625 8.55418 13.4457 8.48311 13.4003C8.41204 13.355 8.3623 13.2847 8.34455 13.2045C8.32681 13.1243 8.34247 13.0407 8.38818 12.9715L12.7192 6.68085C12.7681 6.61389 12.8423 6.56793 12.926 6.55277C13.0097 6.53762 13.0962 6.55447 13.1671 6.59974C13.238 6.64501 13.2877 6.7151 13.3056 6.79506C13.3234 6.87502 13.308 6.95852 13.2627 7.02772L8.9316 13.3183ZM13.0772 13.2746C10.8384 13.2058 10.8449 10.0965 13.0772 10.0277C15.3127 10.0933 15.3159 13.2058 13.0772 13.2746ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.3511 12.647 13.6129 12.5406 13.8056 12.3537C13.9984 12.1667 14.1065 11.9142 14.1065 11.6512C14.1065 11.3881 13.9984 11.1356 13.8056 10.9487C13.6129 10.7617 13.3511 10.6554 13.0772 10.6527ZM9.61494 8.34647C9.61119 8.08363 9.49983 7.83277 9.30496 7.64817C9.11009 7.46357 8.84738 7.36007 8.57366 7.36007C8.29994 7.36007 8.03723 7.46357 7.84236 7.64817C7.64749 7.83277 7.53613 8.08363 7.53239 8.34647C7.53613 8.60932 7.64749 8.86018 7.84236 9.04478C8.03723 9.22938 8.29994 9.33288 8.57366 9.33288C8.84738 9.33288 9.11009 9.22938 9.30496 9.04478C9.49983 8.86018 9.61119 8.60932 9.61494 8.34647ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM10.8254 3.57147C1.94855 3.9246 1.94529 16.0746 10.8254 16.4277C12.6006 16.4277 14.3031 15.7505 15.5584 14.545C16.8137 13.3395 17.5189 11.7044 17.5189 9.9996C17.5189 8.29475 16.8137 6.65974 15.5584 5.45423C14.3031 4.24872 12.6006 3.57147 10.8254 3.57147ZM10.2657 8.34647C10.1941 10.4933 6.94992 10.4933 6.88159 8.34647C6.95318 6.20272 10.1941 6.20272 10.2657 8.34647ZM8.38818 12.9715L12.7192 6.68085C12.7681 6.61389 12.8423 6.56793 12.926 6.55277C13.0097 6.53762 13.0962 6.55447 13.1671 6.59974C13.238 6.64501 13.2877 6.7151 13.3056 6.79506C13.3234 6.87502 13.308 6.95852 13.2627 7.02772L8.9316 13.3183C8.88285 13.3856 8.8086 13.4319 8.72477 13.4472C8.64093 13.4625 8.55418 13.4457 8.48311 13.4003C8.41204 13.355 8.3623 13.2847 8.34455 13.2045C8.32681 13.1243 8.34247 13.0407 8.38818 12.9715ZM13.0772 10.0277C15.3127 10.0933 15.3159 13.2058 13.0772 13.2746C10.8384 13.2058 10.8449 10.0965 13.0772 10.0277ZM13.0772 12.6496C13.3514 12.6474 13.6137 12.5413 13.8069 12.3543C14 12.1673 14.1084 11.9146 14.1084 11.6512C14.1084 11.3877 14 11.135 13.8069 10.948C13.6137 10.761 13.3514 10.6549 13.0772 10.6527C12.8027 10.6546 12.5402 10.7606 12.3468 10.9476C12.1534 11.1347 12.0449 11.3876 12.0449 11.6512C12.0449 11.9147 12.1534 12.1676 12.3468 12.3547C12.5402 12.5417 12.8027 12.6477 13.0772 12.6496ZM9.61494 8.34647C9.61119 8.08363 9.49983 7.83277 9.30496 7.64817C9.11009 7.46357 8.84738 7.36007 8.57366 7.36007C8.29994 7.36007 8.03723 7.46357 7.84236 7.64817C7.64749 7.83277 7.53613 8.08363 7.53239 8.34647C7.53613 8.60932 7.64749 8.86018 7.84236 9.04478C8.03723 9.22938 8.29994 9.33288 8.57366 9.33288C8.84738 9.33288 9.11009 9.22938 9.30496 9.04478C9.49983 8.86018 9.61119 8.60932 9.61494 8.34647ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM13.0772 10.6527C12.8034 10.6556 12.5418 10.762 12.3492 10.9489C12.1567 11.1359 12.0487 11.3882 12.0487 11.6512C12.0487 11.9141 12.1567 12.1664 12.3492 12.3534C12.5418 12.5403 12.8034 12.6468 13.0772 12.6496C13.351 12.6468 13.6125 12.5403 13.8051 12.3534C13.9977 12.1664 14.1057 11.9141 14.1057 11.6512C14.1057 11.3882 13.9977 11.1359 13.8051 10.9489C13.6125 10.762 13.351 10.6556 13.0772 10.6527ZM19.2402 10.5246C19.0059 9.95897 19.302 9.2746 19.4908 8.73397C19.6498 8.26631 19.6502 7.76222 19.4917 7.29438C19.3332 6.82653 19.0241 6.41911 18.6089 6.13085C18.1241 5.79335 17.4733 5.41835 17.3106 4.8246L17.0633 4.08397C16.9054 3.61588 16.597 3.20793 16.1825 2.91875C15.7679 2.62958 15.2686 2.47407 14.7562 2.4746H13.946C13.318 2.5121 12.7257 2.03085 12.2507 1.69335C11.8365 1.40395 11.3376 1.24805 10.8254 1.24805C10.3133 1.24805 9.81429 1.40395 9.40017 1.69335C8.91858 2.03397 8.33937 2.5121 7.70485 2.4746H6.8946C6.38225 2.47407 5.88291 2.62958 5.46837 2.91875C5.05383 3.20793 4.74544 3.61588 4.58753 4.08397L4.34023 4.8246C4.17753 5.4121 3.52348 5.7996 3.04189 6.13085C2.62675 6.41911 2.31765 6.82653 2.15916 7.29438C2.00068 7.76222 2.00099 8.26631 2.16006 8.73397L2.41061 9.4746C2.6449 10.0402 2.34879 10.7246 2.16006 11.2652C2.00099 11.7329 2.00068 12.237 2.15916 12.7048C2.31765 13.1727 2.62675 13.5801 3.04189 13.8684C3.52673 14.2059 4.17753 14.5809 4.34023 15.1746L4.58753 15.9152C4.74544 16.3833 5.05383 16.7913 5.46837 17.0805C5.88291 17.3696 6.38225 17.5251 6.8946 17.5246H7.70485C8.33289 17.4871 8.92509 17.9684 9.40017 18.3059C9.81491 18.5938 10.3136 18.7488 10.8254 18.7488C11.3372 18.7488 11.8359 18.5938 12.2507 18.3059L12.9047 17.8496C13.3961 17.4621 14.1608 17.5184 14.7562 17.5246C15.2686 17.5251 15.7679 17.3696 16.1825 17.0805C16.597 16.7913 16.9054 16.3833 17.0633 15.9152C17.2423 15.3652 17.4147 14.6527 17.9549 14.3246L18.6089 13.8684C19.0241 13.5801 19.3332 13.1727 19.4917 12.7048C19.6502 12.237 19.6498 11.7329 19.4908 11.2652L19.2402 10.5246ZM10.2657 8.34647C10.1941 10.4933 6.94992 10.4933 6.88159 8.34647C6.95318 6.20272 10.1941 6.20272 10.2657 8.34647ZM8.38818 12.9715L12.7192 6.68085C12.7681 6.61389 12.8423 6.56793 12.926 6.55277C13.0097 6.53762 13.0962 6.55447 13.1671 6.59974C13.238 6.64501 13.2877 6.7151 13.3056 6.79506C13.3234 6.87502 13.308 6.95852 13.2627 7.02772L8.9316 13.3183C8.88285 13.3856 8.8086 13.4319 8.72477 13.4472C8.64093 13.4625 8.55418 13.4457 8.48311 13.4003C8.41204 13.355 8.3623 13.2847 8.34455 13.2045C8.32681 13.1243 8.34247 13.0407 8.38818 12.9715ZM13.0772 10.0277C15.3127 10.0933 15.3159 13.2058 13.0772 13.2746C10.8384 13.2058 10.8449 10.0965 13.0772 10.0277ZM13.0772 12.6496C13.3514 12.6474 13.6137 12.5413 13.8069 12.3543C14 12.1673 14.1084 11.9146 14.1084 11.6512C14.1084 11.3877 14 11.135 13.8069 10.948C13.6137 10.761 13.3514 10.6549 13.0772 10.6527C12.8027 10.6546 12.5402 10.7606 12.3468 10.9476C12.1534 11.1347 12.0449 11.3876 12.0449 11.6512C12.0449 11.9147 12.1534 12.1676 12.3468 12.3547C12.5402 12.5417 12.8027 12.6477 13.0772 12.6496ZM9.61494 8.34647C9.61119 8.08363 9.49983 7.83277 9.30496 7.64817C9.11009 7.46357 8.84738 7.36007 8.57366 7.36007C8.29994 7.36007 8.03723 7.46357 7.84236 7.64817C7.64749 7.83277 7.53613 8.08363 7.53239 8.34647C7.53613 8.60932 7.64749 8.86018 7.84236 9.04478C8.03723 9.22938 8.29994 9.33288 8.57366 9.33288C8.84738 9.33288 9.11009 9.22938 9.30496 9.04478C9.49983 8.86018 9.61119 8.60932 9.61494 8.34647Z"
                fill="url(#paint0_linear_2901_18803)"
            />
            <defs>
                <linearGradient
                    id="paint0_linear_2901_18803"
                    x1="2.04064"
                    y1="9.99835"
                    x2="19.6102"
                    y2="9.99835"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#FF8200" />
                    <stop offset="1" stopColor="#E46E00" />
                </linearGradient>
            </defs>
        </svg>
    )
}

export default CouponStartSvg
