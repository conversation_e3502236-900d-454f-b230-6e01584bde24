import React from "react";

const RefundSvg = () => {
    return (

            <svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 70 70" fill="none" >
                <g clipPath="url(#clip0_2213_6163)">
                    <path
                        d="M39.3242 23.2216H35.0856V14.9026C35.0856 14.424 34.6963 14.0348 34.2177 14.0348H20.807C20.3285 14.0348 19.9392 14.424 19.9392 14.9026V23.2216H15.7007V14.9026C15.7007 12.087 17.9913 9.79639 20.8069 9.79639H34.2176C37.0332 9.79639 39.3239 12.087 39.3239 14.9026L40.3322 18.9952L39.3242 23.2216Z"
                        fill="url(#paint0_linear_2213_6163)" />
                    <path
                        d="M34.2176 9.79639H31.8696C34.6852 9.79639 36.9759 12.087 36.9759 14.9026V23.2216H39.3239L40.3322 18.9952L39.3239 14.9026C39.3239 12.087 37.0332 9.79639 34.2176 9.79639Z"
                        fill="#333C63" />
                    <path
                        d="M51.7822 62.54V62.4901C51.7822 61.1169 51.6801 59.7455 51.4765 58.3874L46.3773 24.3583C46.1706 22.8983 44.9208 21.813 43.4463 21.813H11.711C10.2404 21.813 8.99266 22.8928 8.78143 24.3483L3.56328 58.3392C3.34986 59.7293 3.24268 61.1337 3.24268 62.54L27.5124 65.5041L51.7822 62.54Z"
                        fill="#F6A96C" />
                    <path
                        d="M51.7827 62.4904V62.5397L47.8363 63.0223L46.8503 62.5397V62.4904C46.8503 61.1172 46.7489 59.7453 46.5446 58.3876L41.4461 24.358C41.2391 22.8988 39.9898 21.813 38.5151 21.813H43.4462C44.9208 21.813 46.1702 22.8989 46.3771 24.358L51.4772 58.3876C51.6799 59.7453 51.7827 61.117 51.7827 62.4904Z"
                        fill="#EA9B58" />
                    <path
                        d="M20.7116 55.2228C20.1282 55.2228 19.6553 54.7499 19.6553 54.1665V30.1862C19.6553 29.6028 20.1282 29.1299 20.7116 29.1299C21.2949 29.1299 21.7679 29.6028 21.7679 30.1862V54.1664C21.7679 54.7499 21.2949 55.2228 20.7116 55.2228Z"
                        fill="#E2DEE2" />
                    <path
                        d="M34.3136 55.2228C33.7302 55.2228 33.2573 54.7499 33.2573 54.1665V39.8302C33.2573 39.2468 33.7302 38.7739 34.3136 38.7739C34.897 38.7739 35.3699 39.2468 35.3699 39.8302V54.1664C35.3699 54.7499 34.897 55.2228 34.3136 55.2228Z"
                        fill="#E2DEE2" />
                    <path
                        d="M34.3137 40.8867C31.6314 40.8867 29.457 38.7124 29.457 36.0301V33.9865C29.457 31.3043 31.6314 29.1299 34.3137 29.1299C36.996 29.1299 39.1704 31.3043 39.1704 33.9865V36.0301C39.1704 38.7124 36.9958 40.8867 34.3137 40.8867Z"
                        fill="#F3F0F3" />
                    <path
                        d="M3.24268 62.54H51.7822V67.0397C51.7822 68.6746 50.4569 70 48.822 70H6.20291C4.56803 70 3.24268 68.6746 3.24268 67.0397V62.54Z"
                        fill="#EA9B58" />
                    <path
                        d="M46.8504 62.54V67.0397C46.8504 68.6746 45.525 70 43.8901 70H48.8219C50.4567 70 51.7821 68.6746 51.7821 67.0397V62.54H46.8504Z"
                        fill="#D88A55" />
                    <path
                        d="M64.1046 25.2741H41.9767C40.5118 25.2741 39.3242 24.0866 39.3242 22.6216V2.65248C39.3242 1.18754 40.5118 0 41.9767 0H64.1046C65.5696 0 66.7571 1.18754 66.7571 2.65248V22.6218C66.7571 24.0866 65.5696 25.2741 64.1046 25.2741Z"
                        fill="#FF4B55" />
                    <path
                        d="M64.1044 0H60.5806C62.0455 0 63.233 1.18754 63.233 2.65248V22.6218C63.233 24.0867 62.0455 25.2742 60.5806 25.2742H64.1044C65.5693 25.2742 66.7568 24.0867 66.7568 22.6218V2.65248C66.7568 1.18754 65.5693 0 64.1044 0Z"
                        fill="#FF4B55" />
                    <path
                        d="M57.3378 12.959C57.1589 12.7815 57.1589 12.4928 57.3378 12.314L59.9983 9.65341C60.5729 9.07878 60.5729 8.14923 59.9983 7.57446L58.1026 5.67872C57.5293 5.10546 56.5984 5.10546 56.0251 5.67872L55.2138 6.49001L53.3632 8.34063C53.1851 8.51878 52.8963 8.51878 52.7183 8.34063L50.0567 5.67899C49.4827 5.10505 48.5522 5.10505 47.9784 5.67899L46.0828 7.5746C45.5089 8.14854 45.5089 9.07905 46.0828 9.65286L48.7444 12.3145C48.9226 12.4926 48.9226 12.7814 48.7444 12.9594L46.0828 15.621C45.5089 16.195 45.5089 17.1255 46.0828 17.6993L47.9784 19.5949C48.5524 20.1689 49.4829 20.1689 50.0567 19.5949L52.7183 16.9333C52.8965 16.7551 53.1852 16.7551 53.3632 16.9333L55.2138 18.7829L56.0251 19.5942C56.5984 20.1689 57.5293 20.1689 58.1026 19.5942L59.9983 17.6999C60.5729 17.1252 60.5729 16.1942 59.9983 15.6209L57.3378 12.959Z"
                        fill="#F3F0F3" />
                    <path
                        d="M20.7115 40.8866C17.9324 40.8866 15.6714 38.6255 15.6714 35.8463V30.1862C15.6714 29.6028 16.1443 29.1299 16.7277 29.1299C17.3111 29.1299 17.784 29.6028 17.784 30.1862V35.8463C17.784 37.4606 19.0973 38.7739 20.7114 38.7739C22.3256 38.7739 23.6389 37.4606 23.6389 35.8463V30.1862C23.6389 29.6028 24.1119 29.1299 24.6952 29.1299C25.2786 29.1299 25.7515 29.6028 25.7515 30.1862V35.8463C25.7518 38.6255 23.4907 40.8866 20.7115 40.8866Z"
                        fill="#F3F0F3" />
                </g>
                <defs>
                    <linearGradient id="paint0_linear_2213_6163" x1="15.7007" y1="16.509" x2="40.3322" y2="16.509"
                                    gradientUnits="userSpaceOnUse">
                        <stop stopColor="#474F72" />
                        <stop offset="1" stopColor="#525263" />
                    </linearGradient>
                    <clipPath id="clip0_2213_6163">
                        <rect width="70" height="70" fill="white" />
                    </clipPath>
                </defs>
            </svg>

    );
};

export default RefundSvg;
