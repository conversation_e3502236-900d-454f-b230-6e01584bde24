importScripts(
    'https://www.gstatic.com/firebasejs/9.13.0/firebase-app-compat.js'
)
importScripts(
    'https://www.gstatic.com/firebasejs/9.13.0/firebase-messaging-compat.js'
)
firebase?.initializeApp({
    apiKey: 'AIzaSyB8uHM9tXLWgA7QSyum-6RpbpUFRXBHnU0',
    authDomain: 'delivery-add06.firebaseapp.com',
    projectId: 'delivery-add06',
    storageBucket: 'delivery-add06.firebasestorage.app',
    messagingSenderId: '13815378193',
    appId: '1:13815378193:web:2b49f7173257c55771dcb0',
    measurementId: 'G-LG6T8S8V10',
})

// Retrieve firebase messaging
const messaging = firebase?.messaging()

messaging.onBackgroundMessage(function (payload) {
    console.log('[firebase-messaging-sw.js] Received background message ', payload);
    
    const notificationTitle = payload.notification.title || 'UnderKitchen Notification'
    const notificationOptions = {
        body: payload.notification.body || 'You have a new notification',
        icon: '/logo.png',
        badge: '/logo.png',
        tag: 'underkitchen-notification',
        requireInteraction: true,
    }

    self.registration.showNotification(notificationTitle, notificationOptions)
})

// Handle notification clicks
self.addEventListener('notificationclick', function(event) {
    console.log('[firebase-messaging-sw.js] Notification click received.');
    event.notification.close();
    event.waitUntil(
        clients.openWindow('https://underkitchen.it')
    );
});
