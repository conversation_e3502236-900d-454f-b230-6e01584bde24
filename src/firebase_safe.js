import { initializeApp, getApps, getApp } from 'firebase/app'
import {
    getMessaging,
    getToken,
    onMessage,
    isSupported,
} from 'firebase/messaging'
import { getAuth } from 'firebase/auth'

const firebaseConfig = {
    apiKey: 'AIzaSyB8uHM9tXLWgA7QSyum-6RpbpUFRXBHnU0',
    authDomain: 'delivery-add06.firebaseapp.com',
    projectId: 'delivery-add06',
    storageBucket: 'delivery-add06.firebasestorage.app',
    messagingSenderId: '13815378193',
    appId: '1:13815378193:web:2b49f7173257c55771dcb0',
    measurementId: 'G-LG6T8S8V10',
}
const firebaseApp = !getApps().length ? initializeApp(firebaseConfig) : getApp()

// Safe messaging initialization
const messaging = (async () => {
    try {
        // Check if we're in a browser environment
        if (typeof window === 'undefined') return null
        
        // Check if notifications are supported
        if (!('Notification' in window)) {
            console.log('Notifications not supported')
            return null
        }
        
        // Check permission status before initializing
        if (Notification.permission === 'denied') {
            console.log('Notification permission denied, skipping messaging')
            return null
        }
        
        const isSupportedBrowser = await isSupported()
        if (isSupportedBrowser) {
            return getMessaging(firebaseApp)
        }
        return null
    } catch (err) {
        console.log('Messaging initialization failed:', err)
        return null
    }
})()

export const fetchToken = async (setFcmToken) => {
    try {
        // Early return if notifications not supported or denied
        if (typeof window === 'undefined' || !('Notification' in window)) {
            console.log('Notifications not supported')
            setFcmToken && setFcmToken()
            return
        }

        if (Notification.permission === 'denied') {
            console.log('Notification permission denied')
            setFcmToken && setFcmToken()
            return
        }

        const messagingInstance = await messaging
        if (!messagingInstance) {
            console.log('Messaging not available')
            setFcmToken && setFcmToken()
            return
        }

        // Only request permission if it's default
        if (Notification.permission === 'default') {
            const permission = await Notification.requestPermission()
            if (permission !== 'granted') {
                console.log('Permission not granted')
                setFcmToken && setFcmToken()
                return
            }
        }

        return getToken(messagingInstance, {
            vapidKey: 'BAoSMg62g1OvPPI2LQodXwIFXHgzovTtrW5FZNalqD_-1vbGQ8etOonRoMBSFozHtlWKqeJngcm1atK8BcQfd6c',
        })
            .then((currentToken) => {
                if (currentToken) {
                    console.log('FCM Token received')
                    setFcmToken && setFcmToken(currentToken)
                } else {
                    console.log('No token available')
                    setFcmToken && setFcmToken()
                }
            })
            .catch((err) => {
                console.log('Token retrieval failed:', err.message)
                setFcmToken && setFcmToken()
            })
    } catch (error) {
        console.log('fetchToken error:', error.message)
        setFcmToken && setFcmToken()
    }
}

export const onMessageListener = async () =>
    new Promise((resolve) =>
        (async () => {
            try {
                const messagingResolve = await messaging
                if (messagingResolve) {
                    onMessage(messagingResolve, (payload) => {
                        resolve(payload)
                    })
                }
            } catch (error) {
                console.log('Message listener error:', error.message)
            }
        })()
    )

export const auth = getAuth(firebaseApp)
