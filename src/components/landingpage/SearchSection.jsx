import React from 'react'
import { Box, Stack, Typography } from '@mui/material'
import { useTheme } from '@mui/material/styles'
import useMediaQuery from '@mui/material/useMediaQuery'
import { useTranslation } from 'react-i18next'
import HeroLocationForm from './HeroLocationForm'
import CustomContainer from '../container'
import { CustomStackFullWidth } from '@/styled-components/CustomStyles.style'

const SearchSection = ({ handleModalClose }) => {
    const theme = useTheme()
    const { t } = useTranslation()
    const isXSmall = useMediaQuery(theme.breakpoints.down('sm'))
    const isMedium = useMediaQuery(theme.breakpoints.down('md'))

    return (
        <Box
            sx={{
                backgroundColor: theme.palette.background.paper,
                borderTop: `1px solid ${theme.palette.divider}`,
                py: { xs: 3, sm: 4, md: 5 },
                position: 'relative',
                zIndex: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}
        >
            <CustomContainer>
                <CustomStackFullWidth
                    alignItems="center"
                    justifyContent="center"
                    spacing={{ xs: 2, sm: 3 }}
                    sx={{
                        maxWidth: { xs: '100%', sm: '600px', md: '700px' },
                        margin: '0 auto',
                        px: { xs: 2, sm: 0 }
                    }}
                >
                    {/* Optional: Add a title for the search section */}
                    <Typography
                        variant="h6"
                        sx={{
                            fontSize: { xs: '16px', sm: '18px', md: '20px' },
                            fontWeight: 600,
                            color: theme.palette.text.primary,
                            textAlign: 'center',
                            mb: 1,
                            display: { xs: 'block', sm: 'none' } // Only show on mobile
                        }}
                    >
                        {t('Find restaurants near you')}
                    </Typography>

                    <HeroLocationForm
                        mobileview={isXSmall ? "true" : "false"}
                        handleModalClose={handleModalClose}
                    />
                </CustomStackFullWidth>
            </CustomContainer>
        </Box>
    )
}

export default SearchSection
