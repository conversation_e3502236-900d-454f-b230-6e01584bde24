import React from 'react'

const TwitterIcon = () => {
    return (
        <svg
            width="25"
            height="25"
            viewBox="0 0 25 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <circle cx="12" cy="13" r="10" fill="white" />
            <g clipPath="url(#clip0_2874_348)">
                <path
                    d="M15.4507 8.00098H17.2908L13.2708 12.5956L18 18.8479H14.297L11.3967 15.0559L8.07816 18.8479H6.23695L10.5368 13.9334L6 8.00098H9.79694L12.4186 11.467L15.4507 8.00098ZM14.8049 17.7465H15.8245L9.24295 9.0445H8.14879L14.8049 17.7465Z"
                    fill="black"
                />
            </g>
            <defs>
                <clipPath id="clip0_2874_348">
                    <rect
                        width="12"
                        height="10.848"
                        fill="white"
                        transform="translate(6 8)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default TwitterIcon
