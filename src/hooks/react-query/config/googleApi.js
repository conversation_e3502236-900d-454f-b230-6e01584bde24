import MainApi from '../../../api/MainApi'

export const GoogleApi = {
    placeApiAutocomplete: (search) => {
        if (search && search !== '') {
            return MainApi.get(
                `/api/v1/config/place-api-autocomplete?search_text=${search}`
            )
        }
    },
    placeApiDetails: (placeId) => {
        return MainApi.get(
            `/api/v1/config/place-api-details?placeid=${placeId}`
        )
    },
    getZoneId: (location) => {
        return MainApi.get(
            `/api/v1/config/get-zone-id?lat=${location?.lat}&lng=${location?.lng}`
        )
    },
    distanceApi: (origin, destination) => {
        return MainApi.get(
            `/api/v1/config/distance-api?origin_lat=${
                origin.latitude
            }&origin_lng=${origin.longitude}&destination_lat=${
                destination.lat ? destination?.lat : destination?.latitude
            }&destination_lng=${
                destination.lng ? destination?.lng : destination?.longitude
            }`
        )
    },
    geoCodeApi: (location) => {
        // Fallback to client-side geocoding if backend fails
        return MainApi.get(
            `/api/v1/config/geocode-api?lat=${location?.lat}&lng=${location?.lng}`
        ).catch(async (error) => {
            console.log('Backend geocoding failed, using client-side fallback:', error.message)
            
            // Client-side geocoding fallback
            try {
                const response = await fetch(
                    `https://maps.googleapis.com/maps/api/geocode/json?latlng=${location?.lat},${location?.lng}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAP_KEY}`
                )
                const data = await response.json()
                
                if (data.status === 'OK' && data.results.length > 0) {
                    // Transform the response to match the expected format
                    return {
                        data: {
                            results: data.results,
                            status: data.status,
                            formatted_address: data.results[0]?.formatted_address
                        }
                    }
                } else {
                    throw new Error(`Geocoding failed: ${data.status}`)
                }
            } catch (clientError) {
                console.error('Client-side geocoding also failed:', clientError)
                // Return a default response to prevent app crashes
                return {
                    data: {
                        results: [],
                        status: 'ERROR',
                        formatted_address: 'Location not found'
                    }
                }
            }
        })
    },
}
