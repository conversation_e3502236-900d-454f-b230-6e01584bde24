import { useMutation } from 'react-query'
import MainApi from '../../../api/MainApi'

const postHandler = async (token) => {
    // Only send the request if we have a valid token
    if (!token || token === '' || token === null || token === undefined) {
        console.log('No Firebase token available, skipping FCM update')
        return { message: 'No token to update' }
    }

    const { data } = await MainApi.post('/api/v1/customer/cm-firebase-token', {
        cm_firebase_token: token,
        _method: 'put',
    })
    return data
}

export const useStoreFcm = () => {
    return useMutation('fcm_token', postHandler)
}
