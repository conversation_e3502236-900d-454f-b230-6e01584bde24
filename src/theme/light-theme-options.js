// Colors
const neutral = {
    100: '#FFFFFF',
    200: '#F3F4F5',
    300: '#F5F6F8',
    400: '#9B9B9B',
    500: '#6B7280',
    600: '#4B5864',
    700: '#686B78',
    800: '#1F2937',
    900: '#414141',
    1000: '#141313',
    1100: '#FBFBFB',
    1200: '#4B566B',
    1300: '#EF78224D',
    1400: 'rgba(255, 255, 255, 0.8)',
    1500: 'rgba(239, 120, 34, 0.3)',
    1600: '#FFEBDD',
    1700: '#fff5cf',
    1800: '#FCFCFC',
}

const table = {
    background: '#F8F8FA',
    textColor: '#031C3A',
}
const paperBoxShadow = '#E5EAF1'
const sectionBg = '#F5F6F8'
// const footerTopBg = '#3e4655'
const footerAppBg = '#414141'
const footerTopBgColor = '#00000050'
const footerCenterBg = '#2f3645'
const footerMiddleBg = '#343c4d'
const nonVeg = '#EE7878'
const cardBackground1 = '#FFFFFF'
const cardBackground2 = '#FFFFFF'

const borderBottomBg = '#D1D5DB'
const navbarBg = '#fff'
const footerTopBg = neutral[1500]
const skeletonColor =
    'linear-gradient(78.58deg, #F5F6F8 3.23%, #FBFBFB 53.13%, #F6F7F8 100%)'
const skeletonColorAfter =
    'linear-gradient(78.58deg, #F5F6F8 3.23%, #FDFDFD 53.13%, #F6F7F8 100%)'
const background = {
    default: '#FFFFFF',
    paper: '#FFFFFF',
    buttonBackground: 'rgba(82, 102, 208, 0.05)',
    profileBackground: '#FBFBFB',
    toaster: '#FFFFFF',
}
const newsletterBG = '#2287FD'

const divider = '#E6E8F0'
// main: '#EF7822',
// secondary main: '#ff903f',
const primary = {
    main: '#FF7918',
    light: 'rgba(255, 121, 24, 0.60)',
    dark: '#a3460a',
    contrastText: '#FFFFFF',
    midPrimary: ' #FFF5ED',
    overLay: '#000000',
}
const searchBoxBg = primary.main
const secondary = {
    main: '#934916',
    light: '#db5c00',
    semiLight: '#FFFAF6',
    dark: '#a2684c',
    contrastText: '#FFFFFF',
}
const whiteContainer = {
    main: '#F9FAFC',
    light: '#EF7822',
    dark: '#ff903f',
    contrastText: '#EF7822',
}
const whiteText = {
    main: '#FFFFFF',
}
const success = {
    main: '#00AB11',
    light: '#00AB11',
    dark: '#2e7d32',
    contrastText: '#FFFFFF',
}

const info = {
    main: '#2196F3',
    light: '#64B6F7',
    dark: '#0B79D0',
    contrastText: '#FFFFFF',
}

const warning = {
    main: '#FFB020',
    light: '#FFBF4C',
    dark: '#B27B16',
    contrastText: '#FFFFFF',
}

const error = {
    main: '#FF686A',
    light: '#FF6D6D',
    dark: '#922E2E',
    contrastText: '#FFFFFF',
    info: '#FCECD3',
    back: '#FFE1E2',
    whiteText: '#FFFFFF',
    pureRed: '#DB3022',
}
const customColor = {
    one: '#FF5E00',
    two: '#2B95FF',
    three: '#FF686A',
    four: '#5266D0',
    five: '#FFE1E2',
    six: '#414141',
    seven: '#2C1D13',
    eight: '#FFBE0B',
    nine: '#FB5607',
    ten: '#FFFFFF',
    eleven: '#039D55',
    twelve: '#334257',
    thirteen: '#FFE5CD',
    fourteen: '#FCEA10',
    fifteen: '#3E594D',
}

const text = {
    primary: '#121828',
    secondary: '#65748B',
    info: '#0788FF',
    custom: '#242424',
    disabled: 'rgba(55, 65, 81, 0.48)',
    footerText: '#F5F6F8',
    formHeader: customColor.twelve,
}

export const lightThemeOptions = {
    typography: {
        allVariants: {
            fontFamily: 'Public Sans',
            textTransform: 'none',
        },
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    color: '#FFFFFF',
                    '&.MuiButton-text': {
                        color: '#EF7822',
                    },
                },
            },
        },
        MuiSkeleton: {
            styleOverrides: {
                root: {
                    background:
                        'linear-gradient(78.58deg, #F5F6F8 3.23%, #FBFBFB 53.13%, #F6F7F8 100%)',
                    '&::after': {
                        background:
                            'linear-gradient(78.58deg, #F5F6F8 3.23%, #FDFDFD 53.13%, #F6F7F8 100%)',
                    },
                },
            },
        },
        MuiAvatar: {
            styleOverrides: {
                root: {
                    backgroundColor: neutral[500],
                    color: '#FFFFFF',
                },
            },
        },
        MuiChip: {
            styleOverrides: {
                root: {
                    '&.MuiChip-filledDefault': {
                        backgroundColor: neutral[200],
                        '& .MuiChip-deleteIcon': {
                            color: neutral[400],
                        },
                    },
                    '&.MuiChip-outlinedDefault': {
                        '& .MuiChip-deleteIcon': {
                            color: neutral[300],
                        },
                    },
                },
            },
        },
        MuiInputBase: {
            styleOverrides: {
                input: {
                    '&::placeholder': {
                        opacity: 1,
                        color: text.secondary,
                    },
                },
            },
        },
        MuiOutlinedInput: {
            styleOverrides: {
                notchedOutline: {
                    borderColor: divider,
                },
            },
        },
        MuiMenu: {
            styleOverrides: {
                paper: {
                    borderColor: divider,
                    borderStyle: 'solid',
                    borderWidth: 1,
                },
            },
        },
        MuiPopover: {
            styleOverrides: {
                paper: {
                    borderColor: divider,
                    borderStyle: 'solid',
                    borderWidth: 1,
                },
            },
        },
        MuiSwitch: {
            styleOverrides: {
                switchBase: {
                    color: neutral[500],
                },
                track: {
                    backgroundColor: neutral[400],
                    opacity: 1,
                },
            },
        },
        // MuiTable: {
        //   styleOverrides: {
        //     root: {
        //       boxShadow: `3px 6px 26px 5px rgba(0,0,0,0.34) !important`,
        //     },
        //   },
        // },
        MuiTableRow: {
            styleOverrides: {
                root: {
                    borderBottom: `2px solid ${neutral[200]}`,
                    // borderRadius: '20%',
                    borderCollapse: `separate !important`,
                    // boxShadow: `0px 2px 3px -1px rgba(0,0,0,0.36)`,
                    '&:last-child td, &:last-child th': { border: 0 },
                    whiteSpace: 'nowrap',
                },
            },
        },
        //boxShadow: `0 3px 10px rgb(0 0 0 / 0.2)`,

        MuiTableCell: {
            styleOverrides: {
                root: {
                    //backgroundColor: 'red',
                },
            },
        },
        MuiTableHead: {
            styleOverrides: {
                root: {
                    backgroundColor: neutral[200],
                    '.MuiTableCell-root': {
                        color: neutral[700],

                        // boxShadow: `0px 2px 3px -1px rgba(0,0,0,0.36)`,
                    },
                },
            },
        },

    },

    palette: {
        action: {
            active: neutral[500],
            focus: 'rgba(55, 65, 81, 0.12)',
            hover: '#ff903f',
            selected: 'rgba(55, 65, 81, 0.08)',
            disabledBackground: 'rgba(55, 65, 81, 0.12)',
            disabled: 'rgba(55, 65, 81, 0.26)',
            cardBackground: 'rgba(65, 83, 179, 0.1)',
        },
        paperBoxShadow,
        background,
        divider,
        error,
        info,
        customColor,
        mode: 'light',
        neutral,
        table,
        primary,
        secondary,
        success,
        text,
        warning,
        whiteContainer,
        footerAppBg,
        footerTopBg,
        footerCenterBg,
        footerMiddleBg,
        newsletterBG,
        whiteText,
        footerTopBgColor,
        nonVeg,
        sectionBg,
        cardBackground1,
        cardBackground2,
        searchBoxBg,
        borderBottomBg,
        navbarBg,
        skeletonColor,
        skeletonColorAfter,
    },

    shadows: [
        'none',
        '0px 1px 1px rgba(100, 116, 139, 0.06), 0px 1px 2px rgba(100, 116, 139, 0.1)',
        '0px 1px 2px rgba(100, 116, 139, 0.12)',
        '0px 1px 4px rgba(100, 116, 139, 0.12)',
        '0px 1px 5px rgba(100, 116, 139, 0.12)',
        '0px 1px 6px rgba(100, 116, 139, 0.12)',
        '0px 2px 6px rgba(100, 116, 139, 0.12)',
        '0px 3px 6px rgba(100, 116, 139, 0.12)',
        '0px 2px 4px rgba(31, 41, 55, 0.06), 0px 4px 6px rgba(100, 116, 139, 0.12)',
        '0px 5px 12px rgba(100, 116, 139, 0.12)',
        '0px 5px 14px rgba(100, 116, 139, 0.12)',
        '0px 5px 15px rgba(100, 116, 139, 0.12)',
        '0px 6px 15px rgba(100, 116, 139, 0.12)',
        '0px 7px 15px rgba(100, 116, 139, 0.12)',
        '0px 8px 15px rgba(100, 116, 139, 0.12)',
        '0px 9px 15px rgba(100, 116, 139, 0.12)',
        '0px 10px 15px rgba(100, 116, 139, 0.12)',
        '0px 12px 22px -8px rgba(100, 116, 139, 0.25)',
        '0px 13px 22px -8px rgba(100, 116, 139, 0.25)',
        '0px 14px 24px -8px rgba(100, 116, 139, 0.25)',
        '0px 10px 10px rgba(31, 41, 55, 0.04), 0px 20px 25px rgba(31, 41, 55, 0.1)',
        '0px 25px 50px rgba(100, 116, 139, 0.25)',
        '0px 25px 50px rgba(100, 116, 139, 0.25)',
        '0px 25px 50px rgba(100, 116, 139, 0.25)',
        '0px 25px 50px rgba(100, 116, 139, 0.25)',
        '0px 10px 20px -5px rgba(51, 66, 87, 0.05)',
        '0px 0px 2px 0px rgba(51, 66, 87, 0.20)',
        '0px 0px 2px 0px rgba(51, 66, 87, 0.40)',
    ],
    shadows2: [
        '0px -1px 2px rgba(42, 42, 42, 0.05), 0px 2px 2px rgba(42, 42, 42, 0.1)',
    ],
}
