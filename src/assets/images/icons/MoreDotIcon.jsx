import { useTheme } from '@mui/material'
import React from 'react'

const MoreDotIcon = ({ size = '33' }) => {
    const theme = useTheme()
    return (
        <svg
            width={size}
            height={size}
            viewBox="0 0 33 33"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M16.5 12.375C16.1353 12.375 15.7856 12.2301 15.5277 11.9723C15.2699 11.7144 15.125 11.3647 15.125 11C15.125 10.6353 15.2699 10.2856 15.5277 10.0277C15.7856 9.76987 16.1353 9.625 16.5 9.625C16.8647 9.625 17.2144 9.76987 17.4723 10.0277C17.7301 10.2856 17.875 10.6353 17.875 11C17.875 11.3647 17.7301 11.7144 17.4723 11.9723C17.2144 12.2301 16.8647 12.375 16.5 12.375ZM16.5 17.875C16.1353 17.875 15.7856 17.7301 15.5277 17.4723C15.2699 17.2144 15.125 16.8647 15.125 16.5C15.125 16.1353 15.2699 15.7856 15.5277 15.5277C15.7856 15.2699 16.1353 15.125 16.5 15.125C16.8647 15.125 17.2144 15.2699 17.4723 15.5277C17.7301 15.7856 17.875 16.1353 17.875 16.5C17.875 16.8647 17.7301 17.2144 17.4723 17.4723C17.2144 17.7301 16.8647 17.875 16.5 17.875ZM15.125 22C15.125 22.3647 15.2699 22.7144 15.5277 22.9723C15.7856 23.2301 16.1353 23.375 16.5 23.375C16.8647 23.375 17.2144 23.2301 17.4723 22.9723C17.7301 22.7144 17.875 22.3647 17.875 22C17.875 21.6353 17.7301 21.2856 17.4723 21.0277C17.2144 20.7699 16.8647 20.625 16.5 20.625C16.1353 20.625 15.7856 20.7699 15.5277 21.0277C15.2699 21.2856 15.125 21.6353 15.125 22Z"
                fill={theme.palette.primary.main}
            />
            <path
                opacity="0.4"
                fillRule="evenodd"
                clipRule="evenodd"
                d="M31.2812 16.5784L31.2812 16.4216C31.2812 13.2467 31.2812 10.7594 31.02 8.81787C30.7532 6.83238 30.195 5.26487 28.9657 4.03425C27.7351 2.80362 26.1676 2.24675 24.1807 1.98C22.2406 1.71875 19.7533 1.71875 16.5784 1.71875L16.4216 1.71875C13.2467 1.71875 10.7594 1.71875 8.81787 1.98C6.83237 2.24675 5.26488 2.805 4.03425 4.03425C2.80362 5.26487 2.24675 6.83237 1.98 8.81925C1.71875 10.7594 1.71875 13.2467 1.71875 16.4216L1.71875 16.5784C1.71875 19.7532 1.71875 22.2406 1.98 24.1821C2.24675 26.1676 2.805 27.7351 4.03425 28.9657C5.26487 30.1964 6.83237 30.7532 8.81925 31.02C10.7594 31.2812 13.2467 31.2812 16.4216 31.2812L16.5784 31.2812C19.7532 31.2812 22.2406 31.2812 24.1821 31.02C26.1676 30.7532 27.7351 30.195 28.9657 28.9657C30.1964 27.7351 30.7532 26.1676 31.02 24.1807C31.2812 22.2406 31.2812 19.7533 31.2812 16.5784ZM27.5069 5.49312C28.2906 6.27687 28.7402 7.33562 28.9767 9.09425C29.216 10.8818 29.2187 13.2303 29.2187 16.5C29.2187 19.7698 29.216 22.1182 28.9767 23.9058C28.7402 25.6644 28.2892 26.7245 27.5069 27.5083C26.7231 28.2906 25.6644 28.7402 23.9057 28.9767C22.1182 29.216 19.7697 29.2187 16.5 29.2187C13.2302 29.2187 10.8817 29.216 9.09425 28.9767C7.33562 28.7402 6.2755 28.2892 5.49175 27.5069C4.70937 26.7231 4.25975 25.6644 4.02325 23.9057C3.784 22.1182 3.78125 19.7697 3.78125 16.5C3.78125 13.2302 3.784 10.8817 4.02325 9.09425C4.25975 7.33562 4.71075 6.2755 5.49313 5.49175C6.27688 4.70937 7.33562 4.25975 9.09425 4.02325C10.8818 3.784 13.2302 3.78125 16.5 3.78125C19.7697 3.78125 22.1182 3.784 23.9058 4.02325C25.6644 4.25975 26.7231 4.71075 27.5069 5.49312Z"
                fill={theme.palette.primary.main}
            />
        </svg>
    )
}

export default MoreDotIcon
