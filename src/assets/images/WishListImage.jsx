import { useTheme } from '@mui/material'
import React from 'react'

const WishListImage = () => {
    const theme = useTheme()
    return (
        <svg
            width="66"
            height="65"
            viewBox="0 0 66 65"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M55.75 3.2497V61.7497C55.7483 62.3238 55.5195 62.8739 55.1135 63.2799C54.7076 63.6858 54.1575 63.9147 53.5834 63.9164H12.4167C11.8426 63.9147 11.2925 63.6858 10.8865 63.2799C10.4806 62.8739 10.2517 62.3238 10.25 61.7497V14.083C10.2485 13.7986 10.3043 13.5167 10.4141 13.2542C10.524 12.9918 10.6855 12.7542 10.8892 12.5555L11.0409 12.4147L21.5817 1.87387L21.7225 1.7222C21.9212 1.51854 22.1588 1.35697 22.4212 1.24716C22.6837 1.13734 22.9655 1.08152 23.25 1.08304H53.5834C54.1575 1.08475 54.7076 1.31358 55.1135 1.71953C55.5195 2.12549 55.7483 2.6756 55.75 3.2497Z"
                fill="url(#paint0_linear_2222_6028)"
                fillOpacity="0.2"
            />
            <path
                d="M25.4166 21.3257C25.2747 24.9191 21.7907 26.621 20.8027 27.0316C20.7207 27.066 20.6327 27.0836 20.5438 27.0836C20.4549 27.0836 20.3669 27.066 20.2849 27.0316C19.2947 26.621 15.8107 24.917 15.6666 21.3257C15.6363 20.6156 15.878 19.9206 16.3424 19.3825C16.8068 18.8445 17.4589 18.5038 18.1659 18.43C18.6039 18.3849 19.0464 18.4423 19.4584 18.5978C19.8703 18.7533 20.2405 19.0026 20.5394 19.3259C20.8385 19.0027 21.2087 18.7536 21.6206 18.5981C22.0325 18.4426 22.475 18.385 22.913 18.43C23.6208 18.5025 24.2742 18.8427 24.7396 19.3809C25.2049 19.9192 25.4471 20.6148 25.4166 21.3257Z"
                fill="#FF6262"
            />
            <path
                d="M25.4166 35.4087C25.2747 39.0021 21.7907 40.7041 20.8027 41.1146C20.7207 41.149 20.6327 41.1666 20.5438 41.1666C20.4549 41.1666 20.3669 41.149 20.2849 41.1146C19.2947 40.7041 15.8107 39 15.6666 35.4087C15.6363 34.6986 15.878 34.0036 16.3424 33.4656C16.8068 32.9275 17.4589 32.5868 18.1659 32.513C18.6039 32.4679 19.0464 32.5253 19.4584 32.6808C19.8703 32.8363 20.2405 33.0856 20.5394 33.4089C20.8385 33.0857 21.2087 32.8366 21.6206 32.6811C22.0325 32.5256 22.475 32.468 22.913 32.513C23.6208 32.5855 24.2742 32.9257 24.7396 33.4639C25.2049 34.0022 25.4471 34.6978 25.4166 35.4087Z"
                fill="#FF6262"
            />
            <path
                d="M25.4166 49.4917C25.2747 53.0851 21.7907 54.7871 20.8027 55.1976C20.7207 55.232 20.6327 55.2496 20.5438 55.2496C20.4549 55.2496 20.3669 55.232 20.2849 55.1976C19.2947 54.7871 15.8107 53.083 15.6666 49.4917C15.6363 48.7816 15.878 48.0866 16.3424 47.5486C16.8068 47.0105 17.4589 46.6698 18.1659 46.596C18.6039 46.5509 19.0464 46.6084 19.4584 46.7639C19.8703 46.9194 20.2405 47.1686 20.5394 47.4919C20.8385 47.1687 21.2087 46.9196 21.6206 46.7641C22.0325 46.6086 22.475 46.551 22.913 46.596C23.6208 46.6685 24.2742 47.0087 24.7396 47.5469C25.2049 48.0852 25.4471 48.7809 25.4166 49.4917Z"
                fill="#FF6262"
            />
            <path
                d="M21.5818 1.87402C21.2594 2.25995 21.083 2.74698 21.0835 3.24986V9.74986C21.0818 10.324 20.853 10.8741 20.447 11.28C20.0411 11.686 19.491 11.9148 18.9168 11.9165H12.4168C11.914 11.916 11.4269 12.0925 11.041 12.4149L21.5818 1.87402Z"
                fill={theme.palette.neutral[500]}
            />
            <path
                d="M50.3333 22.7497H30.8333C30.546 22.7497 30.2705 22.6355 30.0673 22.4324C29.8641 22.2292 29.75 21.9537 29.75 21.6663C29.75 21.379 29.8641 21.1035 30.0673 20.9003C30.2705 20.6971 30.546 20.583 30.8333 20.583H50.3333C50.6206 20.583 50.8962 20.6971 51.0994 20.9003C51.3025 21.1035 51.4167 21.379 51.4167 21.6663C51.4167 21.9537 51.3025 22.2292 51.0994 22.4324C50.8962 22.6355 50.6206 22.7497 50.3333 22.7497Z"
                fill="#CFCFCF"
            />
            <path
                d="M42.75 27.0837H30.8333C30.546 27.0837 30.2705 26.9695 30.0673 26.7664C29.8641 26.5632 29.75 26.2876 29.75 26.0003C29.75 25.713 29.8641 25.4375 30.0673 25.2343C30.2705 25.0311 30.546 24.917 30.8333 24.917H42.75C43.0373 24.917 43.3129 25.0311 43.516 25.2343C43.7192 25.4375 43.8333 25.713 43.8333 26.0003C43.8333 26.2876 43.7192 26.5632 43.516 26.7664C43.3129 26.9695 43.0373 27.0837 42.75 27.0837Z"
                fill="#CFCFCF"
            />
            <path
                d="M50.3333 36.8337H30.8333C30.546 36.8337 30.2705 36.7195 30.0673 36.5164C29.8641 36.3132 29.75 36.0376 29.75 35.7503C29.75 35.463 29.8641 35.1875 30.0673 34.9843C30.2705 34.7811 30.546 34.667 30.8333 34.667H50.3333C50.6206 34.667 50.8962 34.7811 51.0994 34.9843C51.3025 35.1875 51.4167 35.463 51.4167 35.7503C51.4167 36.0376 51.3025 36.3132 51.0994 36.5164C50.8962 36.7195 50.6206 36.8337 50.3333 36.8337Z"
                fill="#CFCFCF"
            />
            <path
                d="M42.75 41.1667H30.8333C30.546 41.1667 30.2705 41.0525 30.0673 40.8494C29.8641 40.6462 29.75 40.3707 29.75 40.0833C29.75 39.796 29.8641 39.5205 30.0673 39.3173C30.2705 39.1141 30.546 39 30.8333 39H42.75C43.0373 39 43.3129 39.1141 43.516 39.3173C43.7192 39.5205 43.8333 39.796 43.8333 40.0833C43.8333 40.3707 43.7192 40.6462 43.516 40.8494C43.3129 41.0525 43.0373 41.1667 42.75 41.1667Z"
                fill="#CFCFCF"
            />
            <path
                d="M50.3333 50.9167H30.8333C30.546 50.9167 30.2705 50.8025 30.0673 50.5994C29.8641 50.3962 29.75 50.1207 29.75 49.8333C29.75 49.546 29.8641 49.2705 30.0673 49.0673C30.2705 48.8641 30.546 48.75 30.8333 48.75H50.3333C50.6206 48.75 50.8962 48.8641 51.0994 49.0673C51.3025 49.2705 51.4167 49.546 51.4167 49.8333C51.4167 50.1207 51.3025 50.3962 51.0994 50.5994C50.8962 50.8025 50.6206 50.9167 50.3333 50.9167Z"
                fill="#CFCFCF"
            />
            <path
                d="M42.75 55.2497H30.8333C30.546 55.2497 30.2705 55.1355 30.0673 54.9324C29.8641 54.7292 29.75 54.4537 29.75 54.1663C29.75 53.879 29.8641 53.6035 30.0673 53.4003C30.2705 53.1971 30.546 53.083 30.8333 53.083H42.75C43.0373 53.083 43.3129 53.1971 43.516 53.4003C43.7192 53.6035 43.8333 53.879 43.8333 54.1663C43.8333 54.4537 43.7192 54.7292 43.516 54.9324C43.3129 55.1355 43.0373 55.2497 42.75 55.2497Z"
                fill="#CFCFCF"
            />
            <path
                d="M51.4165 25.041C57.867 25.041 63.0962 19.8118 63.0962 13.3613C63.0962 6.91081 57.867 1.68164 51.4165 1.68164C44.966 1.68164 39.7368 6.91081 39.7368 13.3613C39.7368 19.8118 44.966 25.041 51.4165 25.041Z"
                fill="#FF6262"
            />
            <path
                d="M55.4788 18.949C55.2787 18.9493 55.0806 18.91 54.8957 18.8334C54.7109 18.7568 54.543 18.6444 54.4017 18.5027L46.2767 10.3777C45.9928 10.0917 45.8337 9.70479 45.8345 9.30175C45.8352 8.89871 45.9956 8.5124 46.2806 8.22741C46.5656 7.94242 46.9519 7.78199 47.355 7.78125C47.758 7.78052 48.1449 7.93954 48.4309 8.22349L56.5559 16.3485C56.7689 16.5615 56.914 16.833 56.9728 17.1285C57.0315 17.4239 57.0014 17.7302 56.8861 18.0086C56.7708 18.2869 56.5756 18.5248 56.3251 18.6922C56.0746 18.8596 55.7801 18.949 55.4788 18.949Z"
                fill="#EAF4F4"
            />
            <path
                d="M47.3538 18.949C47.0525 18.949 46.758 18.8596 46.5075 18.6922C46.257 18.5248 46.0618 18.2869 45.9465 18.0086C45.8312 17.7302 45.8011 17.4239 45.8598 17.1285C45.9186 16.833 46.0636 16.5615 46.2766 16.3485L54.4017 8.22349C54.6877 7.93954 55.0746 7.78052 55.4776 7.78125C55.8806 7.78199 56.267 7.94242 56.552 8.22741C56.8369 8.5124 56.9974 8.89871 56.9981 9.30175C56.9988 9.70479 56.8398 10.0917 56.5559 10.3777L48.4309 18.5027C48.2896 18.6444 48.1217 18.7568 47.9369 18.8334C47.752 18.91 47.5538 18.9493 47.3538 18.949Z"
                fill="#EAF4F4"
            />
            <defs>
                <linearGradient
                    id="paint0_linear_2222_6028"
                    x1="33.003"
                    y1="63.908"
                    x2="33.003"
                    y2="1.05564"
                    gradientUnits="userSpaceOnUse"
                >
                    <stop stopColor="#9B9B9B" />
                    <stop offset="1" stopColor="#DDDDDD" />
                </linearGradient>
            </defs>
        </svg>
    )
}

export default WishListImage
