import { alpha, Paper, styled, TextField, Typography, Box } from '@mui/material'
import { CustomButtonPrimary } from '@/styled-components/CustomButtons.style'

export const CustomBox = styled(Box)(({ theme }) => ({
    width: '100%',
    [theme.breakpoints.down('sm')]: {
        marginTop: '10px',
    },
}))
export const CustomSearchField = styled(Paper)(({ theme }) => ({
    width: '100%',
    border: 'none',
    borderBottomRightRadius: '0px',
    borderTopRightRadius: '0px',
    height: '48px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    [theme.breakpoints.down('sm')]: {
        height: '44px',
        borderRadius: '8px',
        borderBottomRightRadius: '8px',
        borderTopRightRadius: '8px',
    },
}))
export const StyledButton = styled(CustomButtonPrimary)(
    ({ theme, radiuschange, languageDirection }) => ({
        color: `${theme.palette.whiteContainer.main} !important`,
        width: '500px',
        padding: '12px 16px',
        height: '48px',
        fontWeight: '500',
        fontSize: '14px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
            boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
            transform: 'translateY(-1px)',
        },
        [theme.breakpoints.down('sm')]: {
            width: '100%',
            height: '44px',
            padding: '10px 14px',
            fontSize: '13px',
            borderRadius: '8px !important',
        },
        marginLeft: languageDirection === 'rtl' && '15px',
        borderTopLeftRadius:
            (languageDirection === 'ltr' || !languageDirection) &&
            radiuschange === 'true'
                ? '0px'
                : '8px',
        borderBottomLeftRadius:
            (languageDirection === 'ltr' || !languageDirection) &&
            radiuschange === 'true'
                ? '0px'
                : '8px',
        borderTopRightRadius:
            languageDirection === 'rtl' && radiuschange === 'true'
                ? '0px'
                : '8px',
        borderBottomRightRadius:
            languageDirection === 'rtl' && radiuschange === 'true'
                ? '0px'
                : '8px',
    })
)

export const CssTextField = styled(TextField)(
    ({ theme, languageDirection, mobileview, getLocation }) => ({
        ...(getLocation
            ? {} // If getLocation is true, apply no styles
            : {
                width: '100%',
                '& label.Mui-focused': {
                    color: theme.palette.primary.main,
                    background: theme.palette.neutral[100],
                },
                '& .MuiInput-underline:after': {
                    borderBottomColor: theme.palette.primary.main,
                    background: theme.palette.neutral[100],
                },
                '& .MuiOutlinedInput-notchedOutline': {
                    border:
                        mobileview === 'true'
                            ? `.5px solid ${alpha(theme.palette.primary.main, 0.3)}`
                            : 'none',
                },
                '& .MuiOutlinedInput-root': {
                    paddingTop: '0px',
                    paddingBottom: '0px',
                    borderTopRightRadius:
                        mobileview === 'true'
                            ? '5px'
                            : (languageDirection === 'ltr' || !languageDirection) && '0px',
                    borderBottomRightRadius:
                        mobileview === 'true'
                            ? '5px'
                            : (languageDirection === 'ltr' || !languageDirection) && '0px',
                    borderTopLeftRadius:
                        mobileview === 'true'
                            ? '5px'
                            : languageDirection === 'rtl' && '0px',
                    borderBottomLeftRadius:
                        mobileview === 'true'
                            ? '5px'
                            : languageDirection === 'rtl' && '0px',
                    '& fieldset': {
                        borderColor: theme.palette.primary.main,
                    },
                    '&:hover fieldset': {
                        borderColor: theme.palette.primary.main,
                    },
                    '&.Mui-focused fieldset': {
                        borderColor: theme.palette.primary.main,
                    },
                },
            }),
    })
);

export const CustomButton = styled(Paper)(({ theme, backgroundColor }) => ({
    height: '50px',
    borderRadius: '5px',
    overflow: 'hidden',
    cursor: 'pointer',
    backgroundColor: backgroundColor,
    [theme.breakpoints.down('md')]: {
        width: '125px',
        height: '45px',
    },
}))
export const HeroCardTypography = styled(Typography)(({ theme, fontsize }) => ({
    textAlign: 'center',
    color: theme.palette.customColor.seven,
    fontWeight: 600,
    letterSpacing: '0.05em',
}))
