import { t } from 'i18next'

export const searchMockData = [
    {
        id: 1,
        name: t('Sort by'),
        value: 'sort_by',
        isActive: false,
    },
    {
        id: 0,
        name: t('Veg'),
        value: 'veg',
        isActive: false,
    },
    {
        id: 2,
        name: t('Non-Veg'),
        value: 'nonVeg',
        isActive: false,
    },
    {
        id: 3,
        name: t('Ratings 3 +'),
        value: 'ratings',
        isActive: false,
    },
    {
        id: 4,
        name: t('Ratings 4 +'),
        value: 'rating',
        isActive: false,
    },
    {
        id: 5,
        name: t('Ratings 5'),
        value: 'rating5',
        isActive: false,
    },
    {
        id: 6,
        name: 'New Arrivals',
        value: 'new_arrivals',
        isActive: false,
    },
    {
        id: 7,
        name: 'Discounted',
        value: 'discounted',
        isActive: false,
    },
    {
        id: 8,
        name: 'Popular',
        value: 'popular',
        isActive: false,
    },
    {
        id: 9,
        name: 'Currently Open',
        value: 'currentlyAvailable',
        isActive: false,
    },
]
