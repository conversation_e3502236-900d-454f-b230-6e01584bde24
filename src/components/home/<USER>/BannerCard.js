import { CustomStackFullWidth } from '@/styled-components/CustomStyles.style'
import CustomImageContainer from '../../CustomImageContainer'
import Skeleton from '@mui/material/Skeleton'
import Image from 'next/image'
import placeholder from '../../../../public/static/notimage.png'
import CustomNextImage from '@/components/CustomNextImage'

const BannerCard = ({ banner, handleBannerClick, onlyShimmer }) => {
    const bannerImage = banner?.image_full_url
    const isSmall = window.innerWidth < 600

    // Calculate responsive dimensions maintaining 16:9 aspect ratio (1920:1080)
    const getResponsiveDimensions = () => {
        if (isSmall) {
            return { width: 320, height: 180 } // 16:9 ratio for mobile
        }
        return { width: 480, height: 270 } // 16:9 ratio for desktop (increased size)
    }

    const { width, height } = getResponsiveDimensions()

    return (
        <>
            {onlyShimmer ? (
                <div style={{
                    width: '100%',
                    aspectRatio: '16/9',
                    backgroundColor: '#f0f0f0',
                    borderRadius: '16px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <Skeleton
                        width="100%"
                        height="100%"
                        variant="rounded"
                        sx={{aspectRatio: '16 / 9'}}
                    />
                </div>
            ) : (
                <div
                    style={{
                        width: '100%',
                        aspectRatio: '16/9',
                        borderRadius: '16px',
                        overflow: 'hidden',
                        cursor: 'pointer',
                        position: 'relative',
                        backgroundColor: '#f0f0f0'
                    }}
                    onClick={() => handleBannerClick(banner)}
                >
                    <img
                        src={bannerImage}
                        alt="banner"
                        style={{
                            width: '100%',
                            height: '100%',
                            objectFit: 'cover', // This will crop the image to fit 16:9
                            objectPosition: 'center', // Center the cropped image
                            borderRadius: '16px',
                            display: 'block'
                        }}
                        onError={(e) => {
                            e.target.src = placeholder?.src || '/static/notimage.png'
                        }}
                    />
                </div>
            )}
        </>
    )
}

export default BannerCard
