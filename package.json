{"name": "StackFood", "version": "3.0.0", "private": true, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@greatsumini/react-facebook-login": "^3.4.0", "@mui/icons-material": "^5.16.14", "@mui/lab": "^5.0.0-alpha.72", "@mui/material": "^5.8.6", "@mui/styles": "^5.7.0", "@mui/system": "^5.8.0", "@mui/x-date-pickers": "^7.24.1", "@mui/x-date-pickers-pro": "^6.18.1", "@react-google-maps/api": "^2.10.2", "@reduxjs/toolkit": "^1.8.0", "axios": "^1.7.9", "base-64": "^1.0.0", "clsx": "^1.1.1", "date-fns": "^2.28.0", "dayjs": "^1.11.7", "emoji-picker-react": "^4.4.7", "firebase": "^11.2.0", "formik": "^2.2.9", "framer-motion": "^7.2.1", "google-map-react": "^2.1.10", "history": "^5.3.0", "i18next": "^21.6.12", "js-cookie": "^3.0.1", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "moment": "^2.29.1", "next": "^15.1.6", "next-seo": "^5.14.0", "nprogress": "^0.2.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apple-login": "^1.1.6", "react-country-flag": "^3.0.2", "react-date-range": "1.4.0", "react-dom": "^18.2.0", "react-geolocated": "^4.0.3", "react-google-recaptcha": "^2.1.0", "react-hot-toast": "^2.2.0", "react-i18next": "^11.15.5", "react-intersection-observer": "^9.4.3", "react-otp-input": "^3.1.1", "react-phone-input-2": "^2.15.1", "react-player": "^2.16.0", "react-query": "^3.34.19", "react-redux": "^7.2.6", "react-router": "^6.2.1", "react-share": "4", "react-slick": "^0.30.3", "react-ui-scrollspy": "^2.3.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "simplebar": "^6.3.0", "simplebar-react": "^2.3.6", "slick-carousel": "^1.8.1", "styled-components": "^5.3.6", "styled-jsx": "^5.1.0", "stylis": "^4.3.5", "stylis-plugin-rtl": "^2.1.1", "use-debounce": "^9.0.4", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.18.0", "prettier": "2.5.1"}}