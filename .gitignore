# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
get_changed_files.sh
changed_files.zip
*./zip
# next.js
/.next/
/out/
/.vscode/

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# local env files
.env*.local

npm-debug.log*
yarn-debug.log*
yarn-.log*

.yarn
/.idea/
/.idea
.idea