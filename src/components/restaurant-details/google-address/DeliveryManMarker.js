import React from 'react'
import { useTheme } from '@emotion/react'

const DeliveryManMarker = ({ width, height }) => {
    const theme = useTheme()
    return (
        <svg
            width="48"
            height="50"
            viewBox="0 0 365 396"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M267.204 115.278C267.204 162.143 229.16 200.135 182.233 200.135C135.305 200.135 97.2622 162.143 97.2622 115.278C97.2622 68.4131 135.305 30.4209 182.233 30.4209C229.16 30.4209 267.204 68.4131 267.204 115.278Z"
                fill="white"
            />
            <path
                d="M198.025 348.456C198.025 357.226 190.908 364.334 182.127 364.334C173.345 364.334 166.228 357.226 166.228 348.456C166.228 339.687 173.345 332.579 182.127 332.579C190.908 332.579 198.025 339.687 198.025 348.456Z"
                fill="#3478F5"
            />
            <path
                d="M188.251 348.456C188.251 343.956 190.127 339.898 193.138 337.01C190.282 334.266 186.402 332.579 182.127 332.579C173.346 332.579 166.229 339.687 166.229 348.456C166.229 357.223 173.346 364.334 182.127 364.334C186.402 364.334 190.282 362.647 193.138 359.903C190.127 357.015 188.251 352.957 188.251 348.456Z"
                fill="black"
                fillOpacity="0.1"
            />
            <path
                d="M119.178 115.227C119.178 72.0871 151.412 36.4714 193.138 31.0789C189.533 30.6119 185.859 30.3701 182.127 30.3701C135.199 30.3701 97.1562 68.3623 97.1562 115.227C97.1562 162.092 135.199 200.084 182.127 200.084C185.859 200.084 189.533 199.842 193.138 199.375C151.412 193.983 119.178 158.365 119.178 115.227Z"
                fill="#FFEEEF"
            />
            <path
                d="M164.999 119.084V147.695H193.653V119.084H164.999Z"
                fill="#FDCE97"
            />
            <path
                d="M198.426 91.8286C199.634 90.9772 201.042 90.51 202.445 90.4458C203.221 90.4098 203.992 90.4909 204.72 90.6828C207.631 91.4469 210.24 94.0236 210.357 97.5455C210.357 97.5725 210.357 97.6086 210.357 97.6356C210.357 104.972 204.354 109.546 198.429 109.546L198.426 91.8286Z"
                fill="#FDCE97"
            />
            <path
                d="M174.552 57.1045C162.691 57.1045 153.073 66.7097 153.073 78.5543V90.4687H205.583V78.5543C205.583 66.7097 195.965 57.1045 184.104 57.1045H174.552Z"
                fill="#FF6B6B"
            />
            <path
                d="M157.847 133.392C140.697 133.392 126.816 147.249 126.816 164.377V197.741C126.811 199.057 127.875 200.129 129.194 200.134H229.458C230.776 200.129 231.84 199.057 231.835 197.741V164.377C231.835 147.249 217.954 133.392 200.804 133.392H188.874L179.326 140.538L169.778 133.392H157.847Z"
                fill="#FF6B6B"
            />
            <path
                d="M160.23 91.8286C159.021 90.9772 157.614 90.51 156.211 90.4458C155.435 90.4098 154.663 90.4909 153.936 90.6828C151.025 91.4469 148.416 94.0236 148.299 97.5455C148.299 97.5725 148.299 97.6086 148.299 97.6356C148.299 104.972 154.302 109.545 160.227 109.545L160.23 91.8286Z"
                fill="#FDCE97"
            />
            <path
                d="M157.847 90.4688V107.17C157.847 119.014 167.465 128.619 179.326 128.619C191.186 128.619 200.804 119.014 200.804 107.17V90.4688H157.847Z"
                fill="#FDD5A6"
            />
            <path
                d="M126.816 185.831V197.741C126.811 199.057 127.875 200.129 129.194 200.134H229.458C230.776 200.129 231.84 199.057 231.835 197.741V185.831H126.816Z"
                fill="#FDD5A6"
            />
            <path
                d="M150.677 157.234C146.751 157.234 143.521 160.456 143.521 164.377V197.741C143.518 198.373 143.767 198.98 144.213 199.429C144.659 199.878 145.265 200.131 145.898 200.134H212.735C213.05 200.135 213.362 200.074 213.653 199.954C213.944 199.835 214.209 199.658 214.432 199.436C214.655 199.213 214.831 198.949 214.951 198.658C215.071 198.367 215.132 198.055 215.131 197.741V164.377C215.131 160.456 211.905 157.234 207.979 157.234H150.677Z"
                fill="#E6B57C"
            />
            <path
                d="M162.621 157.234V178.684C162.618 179.317 162.866 179.925 163.311 180.374L168.085 185.123C169.018 186.06 170.537 186.06 171.47 185.123L174.552 182.064L177.633 185.123C178.566 186.06 180.085 186.06 181.018 185.123L184.104 182.064L187.186 185.123C188.119 186.06 189.637 186.06 190.571 185.123L195.345 180.374C195.788 179.924 196.035 179.316 196.03 178.684V157.234H162.621Z"
                fill="#FDD5A6"
            />
            <path
                d="M164.984 128.62C164.354 128.625 163.753 128.878 163.31 129.324L158.534 134.093C157.813 134.819 157.636 135.923 158.094 136.838L165.248 151.144C165.874 152.384 167.423 152.83 168.613 152.112L180.56 144.968C181.965 144.123 182.118 142.147 180.86 141.096L166.534 129.166C166.098 128.806 165.549 128.612 164.984 128.62Z"
                fill="#E86262"
            />
            <path
                d="M193.617 128.62C193.07 128.625 192.541 128.818 192.119 129.166L177.794 141.096C176.535 142.147 176.688 144.123 178.093 144.968L190.04 152.112C191.226 152.817 192.762 152.373 193.388 151.144L200.56 136.838C201.013 135.92 200.829 134.815 200.101 134.093L195.344 129.324C194.888 128.865 194.264 128.611 193.617 128.62Z"
                fill="#E86262"
            />
            <path
                d="M179.326 83.3262C164.473 83.3262 154.393 88.3408 154.393 88.3408C153.878 88.5972 153.474 89.0302 153.254 89.5605C153.034 90.0908 153.013 90.6827 153.194 91.2273C153.194 91.2273 153.858 93.2875 155.255 95.3805C156.652 97.4734 159.041 100.009 162.621 100.009H196.03C199.61 100.009 201.999 97.4734 203.396 95.3805C204.793 93.2875 205.457 91.2273 205.457 91.2273C205.639 90.6827 205.618 90.0908 205.398 89.5605C205.178 89.0302 204.773 88.5972 204.259 88.3408C204.259 88.3408 194.178 83.3262 179.326 83.3262Z"
                fill="#E86262"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M184.17 0.0162969C247.609 1.10592 298.7 52.8018 298.7 116.417C298.7 158.084 276.753 194.694 243.717 215.267C240.419 217.321 237.636 220.1 235.518 223.358C236.01 222.599 212.165 255.799 194.31 306.817C190.277 318.342 173.986 318.364 169.953 306.839C156.283 267.791 143.126 245.648 129.789 225.02C126.972 220.662 123.209 217.015 118.85 214.196C85.8726 192.871 64.5492 155.476 65.5902 113.479C67.1684 49.7831 120.381 -1.0761 184.17 0.0162969ZM182.233 200.134C229.16 200.134 267.203 162.142 267.203 115.277C267.203 68.4123 229.16 30.42 182.233 30.42C135.305 30.42 97.2622 68.4123 97.2622 115.277C97.2622 162.142 135.305 200.134 182.233 200.134Z"
                fill="#3478F5"
            />
            <path
                d="M191.975 306.839C178.308 267.791 165.148 245.648 151.813 225.02C148.997 220.662 145.233 217.015 140.872 214.196C107.895 192.868 86.574 155.476 87.615 113.476C89.0818 54.1555 135.344 5.97586 193.219 0.522193C190.235 0.241448 187.218 0.0691101 184.17 0.0162969C120.381 -1.0761 67.1684 49.7831 65.5902 113.479C64.5492 155.476 85.8726 192.871 118.85 214.196C123.209 217.015 126.972 220.662 129.789 225.02C143.126 245.648 156.283 267.791 169.953 306.839C173.668 317.458 187.783 318.266 193.135 309.313C192.684 308.557 192.289 307.737 191.975 306.839Z"
                fill="black"
                fillOpacity="0.1"
            />
            <ellipse
                cx="181.966"
                cy="379.989"
                rx="90.0476"
                ry="16.0108"
                fill="black"
                fillOpacity="0.1"
            />
        </svg>
    )
}

export default DeliveryManMarker
