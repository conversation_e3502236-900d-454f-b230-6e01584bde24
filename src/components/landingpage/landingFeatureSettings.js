export const landingFeatureSettings = {
    speed: 1000,
    slidesToShow: 3,
    slidesToScroll: 1,
    initialSlide: 0,
    autoplay: false,
    infinite: false,
    // nextArrow: hoverOn && <HandleNext />,
    // prevArrow: hoverOn && <HandlePrev />,
    responsive: [
        {
            breakpoint: 3600,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 3200,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 2800,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 2400,
            settings: {
                slidesToShow: 4.7,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 2000,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 1600,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 1340,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 1075,
            settings: {
                slidesToShow: 2.5,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 999,
            settings: {
                slidesToShow: 2.5,
                slidesToScroll: 1,
                infinite: false,
                // dots: false
            },
        },
        {
            breakpoint: 800,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                // initialSlide: 2
                infinite: false,
            },
        },
        {
            breakpoint: 670,
            settings: {
                slidesToShow: 3,
                slidesToScroll: 1,
                initialSlide: 0,
                infinite: false,
            },
        },
        {
            breakpoint: 540,
            settings: {
                slidesToShow: 2.5,
                slidesToScroll: 1,
                infinite: false,
            },
        },
        {
            breakpoint: 450,
            settings: {
                slidesToShow: 2,
                slidesToScroll: 1,
                speed: 1000,
                // dots: false
                initialSlide: 0,
                infinite: true,
            },
        },
    ],
}
