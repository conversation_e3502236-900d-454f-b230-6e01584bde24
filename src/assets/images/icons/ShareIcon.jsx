import { useMediaQuery, useTheme } from '@mui/material'
import React from 'react'

const ShareIcon = (props) => {
    const { width = '16', height = '16' } = props
    const theme = useTheme()
    const isSmall = useMediaQuery(theme.breakpoints.down('md'))
    return (
        <svg
            width={isSmall ? '12' : width}
            height={isSmall ? '12' : height}
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_810_18339)">
                <path
                    d="M0.686954 16.9272C0.670964 16.9272 0.638985 16.9272 0.622995 16.9272C0.607006 16.9272 0.575027 16.9272 0.559037 16.9272C0.303203 16.8632 0.111328 16.6394 0.111328 16.3675C0.111328 16.3675 0.111328 16.3675 0.111328 16.3516C0.111328 8.2768 5.16404 6.02227 7.72238 5.73446V2.26471C7.72238 1.96091 7.97821 1.70508 8.298 1.70508C8.44191 1.70508 8.58582 1.75305 8.68175 1.84898L15.925 8.50066C16.037 8.61259 16.1009 8.77248 16.1009 8.93238C16.1009 9.09227 16.021 9.25217 15.9091 9.34811L8.68175 15.8399C8.58582 15.9198 8.44191 15.9838 8.31399 15.9838C8.21806 15.9838 8.13811 15.9518 8.05816 15.9198H8.07415C7.86628 15.8399 7.72238 15.632 7.72238 15.4082V11.9704C4.79628 12.1463 2.33388 13.9531 1.2306 16.4955L1.21461 16.5434C1.13466 16.7673 0.926798 16.9272 0.686954 16.9272ZM8.298 10.8351C8.61779 10.8351 8.87363 11.091 8.87363 11.4108V14.129L14.6779 8.93238L8.87363 3.60784V6.26211C8.87363 6.58191 8.61779 6.83774 8.298 6.83774C6.55514 6.83774 2.31789 8.19686 1.43846 14.0331C3.08539 12.0663 5.54779 10.8351 8.28201 10.8351C8.298 10.8351 8.298 10.8351 8.298 10.8351Z"
                    fill={theme.palette.primary.main}
                />
            </g>
            <defs>
                <clipPath id="clip0_810_18339">
                    <rect
                        width="15.9896"
                        height="15.9896"
                        fill="white"
                        transform="translate(0.111328 0.985352)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default ShareIcon
