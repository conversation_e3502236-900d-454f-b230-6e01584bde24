import React from 'react'

const CustomerLocationPin = () => {
    return (
        <svg
            width="360"
            height="390"
            viewBox="0 0 360 390"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M294.608 114.653C294.608 52.0018 244.216 1.08915 181.647 0.0160351C118.732 -1.05982 66.248 49.0288 64.6915 111.759C63.6648 153.121 84.6961 189.949 117.222 210.951C121.521 213.727 125.232 217.319 128.01 221.611C141.166 241.926 154.142 263.733 167.624 302.19C171.602 313.54 187.67 313.518 191.648 302.168C209.259 251.924 232.777 219.227 232.291 219.974C234.38 216.766 237.125 214.028 240.379 212.005C272.962 191.745 294.608 155.689 294.608 114.653Z"
                fill="#FF8200"
            />
            <path
                d="M195.313 343.177C195.313 351.814 188.293 358.814 179.632 358.814C170.971 358.814 163.951 351.814 163.951 343.177C163.951 334.54 170.971 327.54 179.632 327.54C188.293 327.54 195.313 334.54 195.313 343.177Z"
                fill="#FF8200"
            />
            <path
                d="M189.345 302.19C175.865 263.733 162.886 241.926 149.734 221.611C146.955 217.319 143.244 213.727 138.942 210.951C106.416 189.946 85.3879 153.121 86.4146 111.757C87.8613 53.3349 133.49 5.8853 190.572 0.514266C187.629 0.237775 184.653 0.0680482 181.647 0.0160351C118.732 -1.05982 66.248 49.0288 64.6915 111.759C63.6648 153.121 84.6961 189.949 117.222 210.951C121.521 213.727 125.232 217.319 128.01 221.611C141.166 241.926 154.142 263.733 167.624 302.19C171.289 312.648 185.21 313.444 190.489 304.627C190.045 303.882 189.655 303.074 189.345 302.19Z"
                fill="black"
                fillOpacity="0.1"
            />
            <path
                d="M185.672 343.177C185.672 338.745 187.522 334.748 190.492 331.904C187.676 329.202 183.849 327.54 179.632 327.54C170.971 327.54 163.951 334.54 163.951 343.177C163.951 351.811 170.971 358.814 179.632 358.814C183.849 358.814 187.676 357.152 190.492 354.45C187.522 351.606 185.672 347.609 185.672 343.177Z"
                fill="black"
                fillOpacity="0.1"
            />
            <path
                d="M263.439 113.482C263.439 159.636 225.917 197.053 179.632 197.053C133.347 197.053 95.8252 159.636 95.8252 113.482C95.8252 67.3267 133.347 29.9102 179.632 29.9102C225.917 29.9102 263.439 67.3267 263.439 113.482Z"
                fill="white"
            />
            <path
                d="M117.546 113.482C117.546 70.995 149.338 35.919 190.492 30.6082C186.937 30.1483 183.313 29.9102 179.632 29.9102C133.347 29.9102 95.8252 67.3267 95.8252 113.482C95.8252 159.636 133.347 197.053 179.632 197.053C183.313 197.053 186.937 196.815 190.492 196.355C149.338 191.044 117.546 155.965 117.546 113.482Z"
                fill="#FFEEEF"
            />
            <path
                d="M179.237 33.8994C157.124 33.8994 139.198 51.7755 139.198 73.8263V89.9723C141.874 87.4976 145.682 86.1534 149.396 86.6489V84.6587C149.396 83.0764 150.752 81.8281 152.323 81.9458C167.803 83.0545 183.558 78.1489 195.47 70.3907C196.582 69.6653 198.053 69.8459 198.959 70.815C209.141 81.7296 209.081 81.308 209.081 82.7315V86.6407C213.155 86.0302 216.767 87.2868 219.279 89.6137V73.8263C219.279 51.7755 201.353 33.8994 179.237 33.8994Z"
                fill="#576574"
            />
            <path
                d="M153.92 73.8263C153.92 54.2831 168.004 38.0303 186.6 34.5838C184.211 34.1403 181.754 33.8994 179.237 33.8994C157.124 33.8994 139.198 51.7755 139.198 73.8263V89.9695C141.874 87.4975 145.679 86.1534 149.396 86.6462V84.6587C149.396 83.0764 150.75 81.8254 152.32 81.9458C152.853 81.9842 153.385 82.0115 153.92 82.0362V73.8263Z"
                fill="#3E4B5A"
            />
            <path
                d="M209.025 85.1915V107.149C209.025 107.39 209.014 107.623 209.009 107.861C216.8 109.038 222.902 103.423 222.902 96.5276C222.902 89.6181 216.794 84.0308 209.025 85.1915Z"
                fill="#FFBEAB"
            />
            <path
                d="M219.323 88.1676C216.803 85.8379 213.182 84.5841 209.1 85.1946V107.152C209.1 107.391 209.086 107.626 209.08 107.861C213.173 108.48 216.8 107.221 219.323 104.888V88.1676Z"
                fill="#F7AF9F"
            />
            <path
                d="M149.487 107.177V85.1729C142.687 84.2667 135.574 89.4735 135.574 96.5281C135.574 103.432 141.679 109.072 149.503 107.889C149.498 107.653 149.487 107.415 149.487 107.177Z"
                fill="#FFBEAB"
            />
            <path
                d="M149.487 104.935C147.186 102.818 145.797 99.8181 145.797 96.5276C145.797 93.3329 147.26 90.5187 149.487 88.4793V85.1723C142.687 84.2689 135.574 89.473 135.574 96.5276C135.574 103.432 141.679 109.071 149.503 107.891C149.498 107.653 149.487 107.415 149.487 107.177V104.935Z"
                fill="#F7AF9F"
            />
            <path
                d="M179.665 135.043C173.195 135.043 167.509 133.272 162.862 130.307V146.666L179.594 167.261L196.323 146.666V129.866C191.582 133.127 185.845 135.043 179.665 135.043Z"
                fill="#FFBEAB"
            />
            <path
                d="M177.787 134.985C172.094 134.678 167.056 132.981 162.861 130.307V146.666L177.787 165.038V134.985Z"
                fill="#F7AF9F"
            />
            <path
                d="M198.959 69.4127C198.053 68.4436 196.581 68.2629 195.469 68.9884C183.558 76.7465 167.803 81.6522 152.322 80.5435C150.752 80.4258 149.396 81.6741 149.396 83.2564V107.109C149.396 122.441 161.442 136.444 179.662 136.444C195.884 136.444 209.08 123.285 209.08 107.109V81.3292C209.08 79.9057 209.141 80.3272 198.959 69.4127Z"
                fill="#FFCDBF"
            />
            <path
                d="M164.119 107.108V80.2725C160.215 80.7159 156.259 80.8254 152.322 80.5435C150.752 80.4258 149.396 81.6741 149.396 83.2564V107.108C149.396 122.441 161.442 136.444 179.662 136.444C182.117 136.444 184.502 136.137 186.781 135.571C172.893 132.294 164.119 120.188 164.119 107.108Z"
                fill="#FFBEAB"
            />
            <path
                d="M196.323 145.177L179.608 165.755L162.862 145.177L118.71 162.256C118.71 162.256 116.538 163.151 113.782 165.18C129.128 184.592 152.921 197.053 179.632 197.053C206.343 197.053 230.136 184.589 245.482 165.18C242.726 163.151 240.555 162.256 240.555 162.256L196.323 145.177Z"
                fill="#E4EAF8"
            />
            <path
                d="M113.782 165.18C115.27 167.063 116.843 168.878 118.485 170.625C121.021 164.569 125.364 160.66 128.596 158.432L118.71 162.256C118.71 162.256 116.541 163.151 113.782 165.18Z"
                fill="#C7D2E5"
            />
            <path
                d="M199.796 176.327L212.295 151.344L196.323 145.177L179.607 165.755L195.908 177.296C197.226 178.232 199.071 177.773 199.796 176.327Z"
                fill="#C7D2E5"
            />
            <path
                d="M159.446 176.327L146.944 151.344L162.861 145.177L179.632 165.755L163.333 177.296C162.013 178.232 160.168 177.773 159.446 176.327Z"
                fill="#C7D2E5"
            />
            <path
                d="M179.629 165.769V165.755L179.621 165.76L179.61 165.755V165.769L167.756 174.165L173.359 182.574H185.883L191.486 174.165L179.629 165.769Z"
                fill="#EE5253"
            />
            <path
                d="M183.088 172.109L185.836 170.162L179.629 165.769V165.755L179.621 165.763L179.61 165.755V165.769L167.756 174.165L173.359 182.574H185.79L182.158 177.124C181.057 175.473 181.471 173.253 183.088 172.109Z"
                fill="#D83941"
            />
            <path
                d="M179.632 197.053C182.314 197.053 184.964 196.922 187.58 196.678L185.883 182.574H173.359L171.663 196.675C174.287 196.922 176.945 197.053 179.632 197.053Z"
                fill="#FF6B6B"
            />
            <path
                d="M179.61 182.574H173.359L171.663 196.675C174.287 196.922 176.945 197.053 179.632 197.053C181.117 197.053 182.594 197.015 184.06 196.938L185.79 182.574H179.61Z"
                fill="#EE5253"
            />
            <ellipse
                cx="179.473"
                cy="374.232"
                rx="88.8141"
                ry="15.7682"
                fill="black"
                fillOpacity="0.1"
            />
        </svg>
    )
}

export default CustomerLocationPin
