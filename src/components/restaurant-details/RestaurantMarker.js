import React from 'react'
import { useTheme } from '@emotion/react'

const RestaurantMarker = ({ width, height }) => {
    const theme = useTheme()
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="50"
            viewBox="0 0 48 50"
            fill="none"
        >
            <path
                d="M38.9631 14.6992C38.9631 6.66702 32.4182 0.13976 24.2916 0.00218099C16.1201 -0.135749 9.30352 6.28587 9.10135 14.3282C8.968 19.631 11.6996 24.3525 15.924 27.0451C16.4824 27.401 16.9644 27.8615 17.3253 28.4118C19.0339 31.0163 20.7193 33.8121 22.4704 38.7425C22.987 40.1976 25.0739 40.1948 25.5906 38.7397C27.8779 32.298 30.9325 28.1061 30.8694 28.2019C31.1407 27.7906 31.4973 27.4396 31.9198 27.1803C36.1517 24.5828 38.9631 19.9602 38.9631 14.6992Z"
                fill="#FF6B6B"
            />
            <path
                d="M26.0667 43.9969C26.0667 45.1042 25.155 46.0016 24.03 46.0016C22.9051 46.0016 21.9934 45.1042 21.9934 43.9969C21.9934 42.8896 22.9051 41.9922 24.03 41.9922C25.155 41.9922 26.0667 42.8896 26.0667 43.9969Z"
                fill="#FF6B6B"
            />
            <path
                d="M25.2914 38.7425C23.5407 33.8121 21.855 31.0163 20.1467 28.4118C19.7859 27.8615 19.3038 27.401 18.7451 27.0451C14.5206 24.3522 11.7894 19.631 11.9228 14.3279C12.1107 6.83794 18.037 0.754651 25.4508 0.0660567C25.0686 0.0306092 24.6821 0.00884933 24.2916 0.00218099C16.1201 -0.135749 9.30352 6.28587 9.10135 14.3282C8.968 19.631 11.6996 24.3525 15.924 27.0451C16.4824 27.401 16.9644 27.8615 17.3253 28.4118C19.0339 31.0163 20.7193 33.8121 22.4704 38.7425C22.9464 40.0832 24.7545 40.1853 25.4401 39.0548C25.3823 38.9594 25.3317 38.8558 25.2914 38.7425Z"
                fill="black"
                fillOpacity="0.1"
            />
            <path
                d="M24.8145 43.9969C24.8145 43.4287 25.0548 42.9163 25.4406 42.5516C25.0748 42.2052 24.5777 41.9922 24.03 41.9922C22.9051 41.9922 21.9934 42.8896 21.9934 43.9969C21.9934 45.1038 22.9051 46.0016 24.03 46.0016C24.5777 46.0016 25.0748 45.7886 25.4406 45.4422C25.0548 45.0775 24.8145 44.5651 24.8145 43.9969Z"
                fill="black"
                fillOpacity="0.1"
            />
            <path
                d="M34.9282 14.5561C34.9282 20.4734 30.0549 25.2704 24.0433 25.2704C18.0318 25.2704 13.1584 20.4734 13.1584 14.5561C13.1584 8.63879 18.0318 3.8418 24.0433 3.8418C30.0549 3.8418 34.9282 8.63879 34.9282 14.5561Z"
                fill="white"
            />
            <path
                d="M30.7931 10.3676C29.3497 8.9468 25.9 11.1844 26.5983 13.2176C26.4233 13.3901 26.5653 13.2502 25.3392 14.4572C22.0807 17.6646 22.8068 16.95 20.7017 19.0221C20.3442 19.3753 20.3445 19.9471 20.7029 20.2996C21.0617 20.6526 21.6433 20.6527 22.002 20.2995C24.4922 17.8484 23.2214 19.1206 26.0808 16.293L26.0948 16.3068C26.8573 15.5183 26.4837 15.8948 27.8978 14.4965C29.9633 15.1841 32.2361 11.7882 30.7931 10.3676Z"
                fill="#FCD170"
            />
            <path
                d="M21.6892 19.406C25.1042 16.0442 23.9518 17.1789 27.5859 13.6016C27.0253 11.9692 29.1403 10.2057 30.7306 10.3102C29.2403 9.00674 25.9132 11.2223 26.5985 13.2176C26.3273 13.4848 21.0042 18.7244 20.7018 19.022C20.0493 19.6668 20.6716 20.7526 21.5703 20.5377C21.3381 20.1876 21.3777 19.7138 21.6892 19.406Z"
                fill="#F9A832"
            />
            <path
                d="M21.6675 14.4797C21.7418 14.5902 21.3363 14.1795 27.5524 20.2983C27.9113 20.6515 28.4929 20.6514 28.8518 20.2984C29.2105 19.9452 29.2104 19.3727 28.8517 19.0196C22.6427 12.9077 23.0539 13.3006 22.9406 13.2266L21.6675 14.4797Z"
                fill="#FF7648"
            />
            <path
                d="M28.069 19.535C28.0689 19.5348 22.301 13.8574 22.301 13.8574L21.6677 14.4801C21.7421 14.5906 21.3366 14.1799 27.5527 20.2987C28.1141 20.8512 29.0755 20.4873 29.1186 19.709C28.7765 19.872 28.3531 19.8146 28.069 19.535Z"
                fill="#E13303"
            />
            <path
                d="M23.6915 11.2602L21.0326 8.64279C20.826 8.43936 20.4911 8.43943 20.2844 8.64273C20.0778 8.8461 20.0778 9.17575 20.2844 9.37905C23.1674 12.2171 23.0467 12.0438 23.0469 12.2423C23.0469 12.4285 22.9899 12.4423 22.3042 13.1171L19.382 10.2408C19.1754 10.0375 18.8404 10.0375 18.6339 10.2408C18.4273 10.4442 18.4273 10.7738 18.6339 10.9772L21.5561 13.8535L20.9169 14.4826C20.7786 14.6187 20.5555 14.6183 20.4177 14.4827L17.7585 11.8654C17.5519 11.6621 17.2171 11.662 17.0104 11.8654C16.8038 12.0688 16.8038 12.3985 17.0105 12.6018L19.6695 15.219C19.9361 15.4814 20.2903 15.6258 20.6672 15.6258C21.0441 15.6258 21.3984 15.4814 21.6649 15.2191L23.6916 13.2242C23.9581 12.9619 24.1048 12.6131 24.1048 12.2422C24.1048 11.8712 23.9579 11.5224 23.6915 11.2602Z"
                fill="#E2E1E0"
            />
            <path
                d="M15.9661 14.5483C15.9661 9.10128 20.0953 4.60435 25.4404 3.92348C24.9787 3.86452 24.5081 3.83398 24.0299 3.83398C18.0184 3.83398 13.145 8.63098 13.145 14.5483C13.145 20.4655 18.0184 25.2625 24.0299 25.2625C24.5081 25.2625 24.9787 25.232 25.4404 25.173C20.0953 24.4922 15.9661 19.9949 15.9661 14.5483Z"
                fill="#FFEEEF"
            />
            <ellipse
                cx="24.0094"
                cy="47.9786"
                rx="11.5353"
                ry="2.02156"
                fill="black"
                fillOpacity="0.1"
            />
        </svg>
    )
}

export default RestaurantMarker
