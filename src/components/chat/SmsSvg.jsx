import { useTheme } from '@mui/material'
import React from 'react'

const SmsSvg = () => {
    const theme = useTheme()
    return (
        <svg
            width="65"
            height="70"
            viewBox="0 0 65 70"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <path
                d="M6.48152 22.8516C6.48152 19.5379 9.16781 16.8516 12.4815 16.8516H41.963C45.2767 16.8516 47.963 19.5379 47.963 22.8516V39.3701C47.963 42.6838 45.2767 45.3701 41.963 45.3701H22.0371L19.4706 48.6552C17.0765 51.7196 12.2457 49.0112 13.6112 45.3701H12.4815C9.16782 45.3701 6.48152 42.6838 6.48152 39.3701V22.8516Z"
                fill={theme.palette.table.background}
            />
            <path
                d="M57.755 33.2227C57.755 29.909 55.0687 27.2227 51.755 27.2227H26.7408C23.4271 27.2227 20.7408 29.9089 20.7408 33.2227V45.9887C20.7408 49.3024 23.4271 51.9887 26.7408 51.9887H43.8747L46.2501 54.9478C48.3965 57.6217 52.626 55.1882 51.3932 51.9887H51.755C55.0687 51.9887 57.755 49.3024 57.755 45.9887V33.2227Z"
                fill={theme.palette.table.background}
            />
            <ellipse
                opacity="0.1"
                cx="32.4074"
                cy="62.8683"
                rx="32.4074"
                ry="1.94444"
                fill="#B9B9B9"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M49.5403 9.82967C49.5403 8.88659 48.7757 8.12207 47.8327 8.12207C46.8896 8.12207 46.125 8.88659 46.125 9.82967V13.3425C46.125 14.2856 46.8896 15.0501 47.8327 15.0501C48.7757 15.0501 49.5403 14.2856 49.5403 13.3425V9.82967ZM47.8327 9.41836C48.0598 9.41836 48.244 9.60251 48.244 9.82967V13.3425C48.244 13.5697 48.0598 13.7538 47.8327 13.7538C47.6055 13.7538 47.4213 13.5697 47.4213 13.3425V9.82967C47.4213 9.60251 47.6055 9.41836 47.8327 9.41836ZM6.48145 22.0363C6.48145 18.8147 9.09312 16.203 12.3148 16.203H42.1296C45.3513 16.203 47.9629 18.8147 47.9629 22.0363V27.2215H53.1481C55.6538 27.2215 57.6851 29.2528 57.6851 31.7585V47.8325C57.6851 50.3383 55.6539 52.3696 53.1481 52.3696H51.4589V54.7178C51.4589 56.4142 49.4383 57.2971 48.1935 56.1446L44.1158 52.3696H26.574C24.0683 52.3696 22.037 50.3383 22.037 47.8325V45.7841L17.0362 50.4515C15.7933 51.6116 13.765 50.7302 13.765 49.03V45.6288H12.3148C9.09312 45.6288 6.48145 43.0172 6.48145 39.7955V22.0363ZM47.9629 39.7955V28.5178H53.1481C54.9379 28.5178 56.3889 29.9687 56.3889 31.7585V47.8325C56.3889 49.6223 54.9379 51.0733 53.1481 51.0733H50.8107C50.4528 51.0733 50.1626 51.3635 50.1626 51.7214V54.7178C50.1626 55.2832 49.4891 55.5775 49.0741 55.1934L44.8101 51.2458C44.6903 51.1349 44.533 51.0733 44.3698 51.0733H26.574C24.7842 51.0733 23.3333 49.6223 23.3333 47.8325V45.6288H42.1296C45.3513 45.6288 47.9629 43.0172 47.9629 39.7955ZM42.1296 44.3325L23.3333 44.3326H22.037L21.9479 44.3325C21.7837 44.3325 21.6257 44.3948 21.5056 44.5069L16.1517 49.5039C15.7374 49.8905 15.0613 49.5968 15.0613 49.03V44.9807C15.0613 44.6227 14.7711 44.3325 14.4132 44.3325H12.3148C9.80904 44.3325 7.77774 42.3013 7.77774 39.7955V22.0363C7.77774 19.5306 9.80904 17.4993 12.3148 17.4993H42.1296C44.6353 17.4993 46.6666 19.5306 46.6666 22.0363V39.7955C46.6666 42.3013 44.6353 44.3325 42.1296 44.3325ZM17.6782 33.9297C19.3427 33.9297 20.6921 32.5803 20.6921 30.9158C20.6921 29.2513 19.3427 27.9019 17.6782 27.9019C16.0137 27.9019 14.6643 29.2513 14.6643 30.9158C14.6643 32.5803 16.0137 33.9297 17.6782 33.9297ZM30.2358 30.9158C30.2358 32.5803 28.8865 33.9297 27.2219 33.9297C25.5574 33.9297 24.208 32.5803 24.208 30.9158C24.208 29.2513 25.5574 27.9019 27.2219 27.9019C28.8865 27.9019 30.2358 29.2513 30.2358 30.9158ZM36.7663 33.9297C38.4308 33.9297 39.7802 32.5803 39.7802 30.9158C39.7802 29.2513 38.4308 27.9019 36.7663 27.9019C35.1018 27.9019 33.7524 29.2513 33.7524 30.9158C33.7524 32.5803 35.1018 33.9297 36.7663 33.9297ZM49.482 17.0791C49.482 18.0222 50.2465 18.7867 51.1896 18.7867H54.7025C55.6456 18.7867 56.4101 18.0222 56.4101 17.0791C56.4101 16.1361 55.6456 15.3715 54.7025 15.3715H51.1896C50.2465 15.3715 49.482 16.1361 49.482 17.0791ZM51.1896 17.4904C50.9625 17.4904 50.7783 17.3063 50.7783 17.0791C50.7783 16.852 50.9625 16.6678 51.1896 16.6678H54.7025C54.9296 16.6678 55.1138 16.852 55.1138 17.0791C55.1138 17.3063 54.9296 17.4904 54.7025 17.4904H51.1896Z"
                fill="#B9B9B9"
            />
        </svg>
    )
}

export default SmsSvg
