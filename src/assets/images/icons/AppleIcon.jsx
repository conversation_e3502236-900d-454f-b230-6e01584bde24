import { useTheme } from '@mui/material'
import React from 'react'

const AppleIcon = () => {
    const theme = useTheme()
    const isSmall = theme.breakpoints.down('md')
    return (
        <svg
            width={isSmall ? '24' : '31'}
            height={isSmall ? '24' : '31'}
            viewBox="0 0 31 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_2987_1333)">
                <path
                    d="M21.4302 0.703125C21.502 0.703125 21.5738 0.703125 21.6496 0.703125C21.8257 2.88564 20.9955 4.51642 19.9866 5.69737C18.9966 6.87017 17.641 8.00763 15.4484 7.83504C15.3021 5.68378 16.1337 4.17396 17.1412 2.99572C18.0757 1.89767 19.7889 0.920562 21.4302 0.703125Z"
                    fill="white"
                />
                <path
                    d="M28.0678 23.4199C28.0678 23.4416 28.0678 23.4606 28.0678 23.481C27.4516 25.3537 26.5727 26.9587 25.5001 28.4481C24.521 29.8003 23.3211 31.6199 21.1786 31.6199C19.3273 31.6199 18.0976 30.4254 16.2003 30.3928C14.1932 30.3602 13.0895 31.3916 11.2545 31.6512C11.0446 31.6512 10.8346 31.6512 10.6288 31.6512C9.28129 31.4555 8.1938 30.3846 7.40155 29.4198C5.06543 26.5686 3.26018 22.8858 2.92432 18.1729C2.92432 17.7108 2.92432 17.2501 2.92432 16.7881C3.06652 13.4151 4.69977 10.6727 6.87068 9.34357C8.01639 8.6369 9.59142 8.03488 11.3452 8.30396C12.0968 8.42083 12.8647 8.67903 13.5378 8.93452C14.1756 9.1805 14.9733 9.61673 15.729 9.59363C16.2409 9.57868 16.7501 9.31096 17.2661 9.12206C18.7775 8.57439 20.259 7.94654 22.2119 8.24144C24.5589 8.59749 26.2246 9.64391 27.2539 11.2584C25.2685 12.5263 23.6989 14.437 23.9671 17.6999C24.2054 20.6639 25.9226 22.3979 28.0678 23.4199Z"
                    fill="white"
                />
            </g>
            <defs>
                <clipPath id="clip0_2987_1333">
                    <rect
                        width="30.8409"
                        height="30.948"
                        fill="white"
                        transform="translate(0.074707 0.703125)"
                    />
                </clipPath>
            </defs>
        </svg>
    )
}

export default AppleIcon
