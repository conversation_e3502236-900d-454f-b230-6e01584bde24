import React from "react";

const HalalSvg = () => {
    return (
        <svg width="20" height="20" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_4147_593)">
                <path
                    d="M25.2937 11.996C25.2937 13.4142 23.4023 14.4462 22.8917 15.6792C22.3622 16.9578 22.9514 19.0198 21.9889 19.9834C21.026 20.9473 18.9635 20.36 17.6858 20.8904C16.4529 21.4022 15.4216 23.2939 14.0038 23.2939C12.585 23.2939 11.5527 21.4024 10.3193 20.8912C9.0403 20.3612 6.97763 20.9492 6.01406 19.9856C5.05051 19.0221 5.63861 16.9595 5.10859 15.6805C4.59744 14.4471 2.70593 13.4148 2.70593 11.996C2.70593 10.5782 4.59764 9.54692 5.10945 8.31394C5.63984 7.03619 5.05267 4.97364 6.01661 4.01077C6.98014 3.0483 9.04206 3.63754 10.3207 3.10801C11.5537 2.5974 12.5857 0.706055 14.0038 0.706055C15.421 0.706055 16.4519 2.59759 17.6845 3.10887C18.9619 3.63878 21.0238 3.05046 21.9866 4.01333C22.9494 4.97616 22.361 7.03796 22.8909 8.31538C23.4022 9.54792 25.2937 10.5788 25.2937 11.996Z"
                    fill="#4FAD40" />
                <path
                    d="M25.2942 11.9963C25.2942 13.413 23.404 14.4467 22.8908 15.6791C22.3632 16.9584 22.9522 19.0184 21.9873 19.9834C21.026 20.9483 18.9624 20.3592 17.6866 20.8905C16.4542 21.4037 15.4206 23.2938 14.0039 23.2938C12.5836 23.2938 11.5536 21.4037 10.3176 20.8905C9.04181 20.3628 6.9782 20.9483 6.01328 19.987C5.48203 19.4521 5.42415 18.5847 5.38806 17.6993C6.34213 17.8981 7.56729 17.7138 8.41659 18.0679C9.55864 18.5414 10.5164 20.2978 11.8319 20.2978C13.1474 20.2978 14.1015 18.5414 15.2436 18.0679C16.4289 17.5764 18.3408 18.1222 19.2334 17.2295C20.1261 16.3332 19.5804 14.425 20.0682 13.2396C20.5417 12.0939 22.2945 11.1398 22.2945 9.8243C22.2945 8.5124 20.5417 7.55467 20.0682 6.41264C19.7141 5.56336 19.8984 4.33819 19.6996 3.3877C20.5851 3.42383 21.4525 3.48166 21.9873 4.01292C22.9487 4.97786 22.3596 7.03787 22.8908 8.31725C23.4004 9.54964 25.2942 10.5796 25.2942 11.9963Z"
                    fill="#4FAD40" />
                <path
                    d="M10.7012 4.3337C10.6493 4.3337 10.6 4.30302 10.5788 4.25201C10.5509 4.18445 10.5829 4.1069 10.6505 4.07899C11.1222 3.88391 11.5646 3.46961 12.0328 3.03085C12.7182 2.38907 13.4268 1.72559 14.2947 1.72559C14.3678 1.72559 14.427 1.78487 14.427 1.85794C14.427 1.93101 14.3678 1.99029 14.2947 1.99029C13.5314 1.99029 12.8615 2.61759 12.2137 3.22421C11.7488 3.65952 11.268 4.11001 10.7518 4.3237C10.7353 4.3306 10.7181 4.3337 10.7012 4.3337Z"
                    fill="white" />
                <path
                    d="M3.90774 12.1284C3.83467 12.1284 3.77539 12.0691 3.77539 11.996C3.77539 11.1075 4.4556 10.3819 5.11357 9.67986C5.56336 9.20008 5.98817 8.74684 6.18911 8.26327C6.21702 8.19571 6.29492 8.164 6.36196 8.19158C6.42952 8.21984 6.46157 8.29705 6.43348 8.3646C6.21427 8.89298 5.75293 9.38516 5.30676 9.86115C4.68394 10.5253 4.0401 11.2123 4.0401 11.996C4.0401 12.0691 3.98081 12.1284 3.90774 12.1284Z"
                    fill="white" />
                <path
                    d="M14.6884 20.4631C14.3036 20.8234 13.7021 20.8234 13.3173 20.4632C13.2973 20.4445 13.2779 20.4263 13.259 20.4086C12.6781 19.8647 12.0197 19.2485 11.1494 18.8878C10.2552 18.5172 9.33442 18.4816 8.52196 18.4505C8.51842 18.4504 8.51485 18.4503 8.51127 18.4501C7.98906 18.4302 7.56946 18.0105 7.54951 17.4883C7.54938 17.4847 7.54924 17.4811 7.5491 17.4775C7.51769 16.665 7.4824 15.7442 7.11182 14.8503C6.75112 13.9796 6.13455 13.3214 5.59067 12.7404C5.57293 12.7215 5.55457 12.7019 5.53575 12.6817C5.17599 12.297 5.17608 11.696 5.53597 11.3114C5.55453 11.2915 5.57263 11.2722 5.59014 11.2535C6.13437 10.6729 6.75112 10.0151 7.11217 9.14474C7.48311 8.25111 7.51893 7.33066 7.55051 6.51856C7.55064 6.51531 7.55077 6.51205 7.55089 6.50878C7.57121 5.9868 7.9907 5.56762 8.51269 5.54766C8.51591 5.54753 8.51912 5.54741 8.52232 5.54729C9.33477 5.51658 10.2556 5.48164 11.1503 5.11142C12.0211 4.75072 12.6795 4.13379 13.2604 3.58992C13.2789 3.57261 13.2981 3.55471 13.3177 3.53637C13.7026 3.17642 14.304 3.17671 14.6885 3.53705C14.7077 3.55504 14.7264 3.57258 14.7445 3.58957C15.3253 4.1338 15.9833 4.75072 16.854 5.11177C17.7478 5.48236 18.6686 5.518 19.4813 5.54906C19.484 5.54916 19.4868 5.54927 19.4896 5.54937C20.0112 5.56938 20.4303 5.98843 20.4503 6.5101C20.4504 6.51293 20.4505 6.51575 20.4506 6.51857C20.4818 7.33102 20.5171 8.25182 20.8881 9.1458C21.2489 10.0161 21.8659 10.6744 22.4101 11.2549C22.4272 11.2732 22.4448 11.292 22.4629 11.3114C22.8229 11.6961 22.8229 12.2972 22.463 12.6819C22.4447 12.7015 22.4268 12.7206 22.4096 12.739C21.8655 13.32 21.2489 13.9785 20.8882 14.8492C20.5176 15.7439 20.4827 16.6651 20.452 17.4775C20.4519 17.4806 20.4518 17.4838 20.4516 17.487C20.4317 18.0088 20.0128 18.4281 19.491 18.4487C19.4876 18.4489 19.4841 18.449 19.4807 18.4491C18.6686 18.4805 17.7484 18.5165 16.8549 18.8871C15.9846 19.2485 15.3267 19.8651 14.7461 20.409C14.7275 20.4265 14.7082 20.4445 14.6884 20.4631Z"
                    fill="#92E884" />
                <path
                    d="M20.4583 6.6982C19.2765 6.1019 17.9429 5.76939 16.5298 5.76939C11.6871 5.76939 7.7694 9.69064 7.7694 14.5298C7.7694 15.9429 8.10187 17.2765 8.69824 18.4583C8.63678 18.4547 8.57894 18.4547 8.52115 18.4511H8.5103C7.98985 18.4294 7.57061 18.0102 7.54895 17.4897V17.4789C7.51644 16.6657 7.48388 15.7442 7.11162 14.8515C6.75022 13.9805 6.13585 13.3227 5.59012 12.7409C5.57203 12.7228 5.55399 12.7011 5.53595 12.6831C5.17454 12.2964 5.17454 11.6964 5.53595 11.3133C5.55399 11.2917 5.57203 11.2736 5.59012 11.2555C6.13585 10.6736 6.75022 10.0159 7.11162 9.14492C7.48388 8.25226 7.52005 7.33066 7.54895 6.5175C7.54895 6.5175 7.54895 6.51388 7.55257 6.51029C7.57061 5.98625 7.98985 5.56701 8.51392 5.54894H8.52115C9.33431 5.51643 10.2559 5.48389 11.1485 5.11166C12.0195 4.75025 12.6809 4.13586 13.2591 3.59014C13.2772 3.57207 13.2989 3.55401 13.3169 3.53592C13.7036 3.17813 14.3036 3.17813 14.6903 3.53592C14.7083 3.55401 14.7264 3.57207 14.7445 3.59014C15.3264 4.13586 15.9841 4.75025 16.8551 5.11166C17.7478 5.48389 18.6694 5.52002 19.4825 5.54894H19.4897C20.0102 5.57062 20.4294 5.98987 20.4511 6.51029V6.5175C20.4547 6.57534 20.4547 6.63677 20.4583 6.6982Z"
                    fill="#78C76B" />
                <path
                    d="M10.5719 11.1274C9.77432 11.1274 9.12549 10.4787 9.12549 9.68112C9.12549 9.53499 9.24406 9.41642 9.39019 9.41642C9.53633 9.41642 9.6549 9.53499 9.6549 9.68112C9.6549 10.1868 10.0663 10.5979 10.5719 10.5979C11.0777 10.5979 11.4891 10.1868 11.4891 9.68112V6.75006C11.4891 6.60392 11.6076 6.48535 11.7538 6.48535C11.8999 6.48535 12.0185 6.60392 12.0185 6.75006V9.68112C12.0185 10.4787 11.3695 11.1274 10.5719 11.1274Z"
                    fill="#363636" />
                <path
                    d="M13.1777 11.1274H12.7448C12.5987 11.1274 12.4801 11.0088 12.4801 10.8626C12.4801 10.7165 12.5987 10.5979 12.7448 10.5979H13.1777C14.0282 10.5979 14.7201 9.90619 14.7201 9.05589V6.75006C14.7201 6.60392 14.8387 6.48535 14.9848 6.48535C15.131 6.48535 15.2495 6.60392 15.2495 6.75006V9.05589C15.2495 10.1981 14.3201 11.1274 13.1777 11.1274Z"
                    fill="#363636" />
                <path
                    d="M16.0725 11.0405C15.7441 11.0405 15.4242 10.9206 15.1754 10.7007C14.8862 10.4446 14.7202 10.0768 14.7202 9.69112V6.75006C14.7202 6.60392 14.8388 6.48535 14.9849 6.48535C15.1311 6.48535 15.2496 6.60392 15.2496 6.75006V9.69112C15.2496 9.92515 15.3504 10.1485 15.5262 10.3039C15.7029 10.4604 15.9381 10.5331 16.1732 10.5049C16.5802 10.4549 16.9607 10.3911 17.3287 10.3298C17.6437 10.2771 17.9441 10.2271 18.2545 10.1857C18.068 9.97548 17.9303 9.77143 17.8066 9.58807C17.5481 9.20514 17.4385 9.07107 17.2241 9.07107C16.8037 9.07107 16.6411 9.57911 16.6395 9.58428C16.5961 9.72352 16.4482 9.80314 16.3083 9.75833C16.1685 9.71525 16.0906 9.5667 16.1339 9.42711C16.1451 9.39092 16.4181 8.54165 17.2241 8.54165C17.739 8.54165 17.9849 8.90597 18.2453 9.292C18.3803 9.49191 18.5331 9.71835 18.7456 9.93653C18.8655 10.0596 18.9048 10.236 18.8481 10.397C18.7916 10.5573 18.6508 10.67 18.4807 10.6903C18.1231 10.7337 17.7795 10.7913 17.4157 10.852C17.0424 10.9144 16.6562 10.9788 16.2376 11.0302C16.1826 11.0371 16.1275 11.0405 16.0725 11.0405Z"
                    fill="#363636" />
                <path
                    d="M14.4412 10.3207C14.3548 10.3207 14.27 10.2783 14.2194 10.2007L12.7487 7.94661C12.6687 7.82391 12.7032 7.66019 12.8257 7.58022C12.9479 7.50026 13.1121 7.53473 13.1919 7.65708L14.6626 9.91122C14.7426 10.0339 14.7081 10.1976 14.5856 10.2776C14.541 10.3069 14.4908 10.3207 14.4412 10.3207Z"
                    fill="#363636" />
                <path
                    d="M10.8537 13.9062C10.8761 13.9062 10.8956 13.9145 10.9121 13.9307C10.9285 13.9473 10.9366 13.9666 10.9366 13.989V15.9736C10.9366 15.9963 10.9285 16.0156 10.9121 16.0322C10.8956 16.0484 10.8761 16.0566 10.8537 16.0566H10.6049C10.5823 16.0566 10.5628 16.0484 10.5464 16.0322C10.5301 16.0156 10.522 15.9963 10.522 15.9736V15.1626H9.59413V15.9736C9.59413 15.9963 9.58585 16.0156 9.56948 16.0322C9.55311 16.0484 9.53364 16.0566 9.51123 16.0566H9.26239C9.23981 16.0566 9.22034 16.0491 9.20396 16.0336C9.18759 16.0184 9.17932 15.9984 9.17932 15.9736V13.989C9.17932 13.9666 9.18759 13.9473 9.20396 13.9307C9.22034 13.9145 9.23981 13.9063 9.26239 13.9063H9.51124C9.53364 13.9063 9.55311 13.9145 9.56949 13.9307C9.58586 13.9473 9.59413 13.9666 9.59413 13.989V14.7786H10.522V13.989C10.522 13.9666 10.5301 13.9473 10.5465 13.9307C10.5628 13.9145 10.5823 13.9063 10.6049 13.9063L10.8537 13.9062Z"
                    fill="#363636" />
                <path
                    d="M12.4266 13.9062C12.4594 13.9062 12.484 13.9149 12.5004 13.9324C12.5167 13.9497 12.531 13.9759 12.5435 14.0107L13.296 15.9522C13.2981 15.9584 13.2991 15.9657 13.2991 15.9736C13.2991 15.9963 13.2909 16.0156 13.2745 16.0322C13.2581 16.0484 13.2387 16.0566 13.2163 16.0566H12.995C12.954 16.0566 12.9253 16.048 12.909 16.0305C12.8926 16.0132 12.8814 15.9963 12.8752 15.9798L12.743 15.6389H11.7723L11.6403 15.9798C11.634 15.9963 11.6228 16.0132 11.6065 16.0305C11.5899 16.048 11.5613 16.0566 11.5205 16.0566H11.2992C11.2766 16.0566 11.2572 16.0484 11.2408 16.0322C11.2244 16.0156 11.2163 15.9963 11.2163 15.9736C11.2163 15.9657 11.2173 15.9584 11.2194 15.9522L11.972 14.0107C11.9842 13.9759 11.9985 13.9497 12.0151 13.9324C12.0315 13.9149 12.0559 13.9062 12.0887 13.9062H12.4266ZM12.5957 15.2549L12.2577 14.3791L11.9198 15.2549H12.5957Z"
                    fill="#363636" />
                <path
                    d="M15.0074 15.6727C15.0298 15.6727 15.0493 15.681 15.0656 15.6972C15.082 15.7137 15.0903 15.733 15.0903 15.7557V15.9736C15.0903 15.9963 15.082 16.0156 15.0656 16.0322C15.0493 16.0484 15.0298 16.0566 15.0074 16.0566H13.6618C13.6392 16.0566 13.6198 16.0484 13.6034 16.0322C13.587 16.0156 13.5787 15.9963 13.5787 15.9736V13.989C13.5787 13.9666 13.587 13.9473 13.6034 13.9307C13.6198 13.9145 13.6392 13.9062 13.6618 13.9062H13.9107C13.9331 13.9062 13.9525 13.9145 13.9689 13.9307C13.9853 13.9473 13.9935 13.9666 13.9935 13.989V15.6727L15.0074 15.6727Z"
                    fill="#363636" />
                <path
                    d="M16.3958 13.9062C16.4286 13.9062 16.4532 13.9149 16.4696 13.9324C16.486 13.9497 16.5003 13.9759 16.5127 14.0107L17.2653 15.9522C17.2673 15.9584 17.2684 15.9657 17.2684 15.9736C17.2684 15.9963 17.2601 16.0156 17.2437 16.0322C17.2274 16.0484 17.2079 16.0566 17.1855 16.0566H16.9642C16.9232 16.0566 16.8946 16.048 16.8782 16.0305C16.8618 16.0132 16.8506 15.9963 16.8444 15.9798L16.7123 15.6389H15.7415L15.6095 15.9798C15.6033 15.9963 15.5921 16.0132 15.5757 16.0305C15.5592 16.048 15.5306 16.0566 15.4897 16.0566H15.2684C15.2459 16.0566 15.2264 16.0484 15.21 16.0322C15.1936 16.0156 15.1855 15.9963 15.1855 15.9736C15.1855 15.9657 15.1866 15.9584 15.1886 15.9522L15.9412 14.0107C15.9535 13.9759 15.9678 13.9497 15.9843 13.9324C16.0007 13.9149 16.0252 13.9062 16.0579 13.9062H16.3958ZM16.5649 15.2549L16.227 14.3791L15.889 15.2549H16.5649Z"
                    fill="#363636" />
                <path
                    d="M18.9765 15.6727C18.9989 15.6727 19.0184 15.681 19.0348 15.6972C19.0511 15.7137 19.0594 15.733 19.0594 15.7557V15.9736C19.0594 15.9963 19.0511 16.0156 19.0348 16.0322C19.0184 16.0484 18.9989 16.0566 18.9765 16.0566H17.6309C17.6083 16.0566 17.5889 16.0484 17.5725 16.0322C17.5561 16.0156 17.5479 15.9963 17.5479 15.9736V13.989C17.5479 13.9666 17.5561 13.9473 17.5725 13.9307C17.5889 13.9145 17.6083 13.9062 17.6309 13.9062H17.8798C17.9022 13.9062 17.9216 13.9145 17.938 13.9307C17.9544 13.9473 17.9627 13.9666 17.9627 13.989V15.6727L18.9765 15.6727Z"
                    fill="#363636" />
                <path
                    d="M18.517 12.8605H9.48187C9.33573 12.8605 9.21716 12.7419 9.21716 12.5958C9.21716 12.4496 9.33573 12.3311 9.48187 12.3311H18.517C18.6632 12.3311 18.7817 12.4496 18.7817 12.5958C18.7817 12.7419 18.6632 12.8605 18.517 12.8605Z"
                    fill="#4FAD40" />
                <path
                    d="M16.168 17.5763H11.831C11.6849 17.5763 11.5663 17.4577 11.5663 17.3116C11.5663 17.1654 11.6849 17.0469 11.831 17.0469H16.168C16.3141 17.0469 16.4327 17.1654 16.4327 17.3116C16.4327 17.4577 16.3141 17.5763 16.168 17.5763Z"
                    fill="#4FAD40" />
            </g>
            <defs>
                <filter id="filter0_d_4147_593" x="0" y="0" width="28" height="28" filterUnits="userSpaceOnUse"
                        colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                   result="hardAlpha" />
                    <feOffset dy="2" />
                    <feGaussianBlur stdDeviation="1" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4147_593" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4147_593" result="shape" />
                </filter>
            </defs>
        </svg>
    );
};

export default HalalSvg;
