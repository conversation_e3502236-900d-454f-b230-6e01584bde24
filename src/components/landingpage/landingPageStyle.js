import { styled } from '@mui/material/styles'
import { alpha, Typography, Box } from '@mui/material'

export const LandingPageTypography = styled(Typography)(
    ({ theme, fontWeight, fontSize, color }) => ({
        color: color ? color : theme.palette.neutral[1000],
        fontWeight: fontWeight ? fontWeight : '400',
        fontSize: fontSize ? fontSize : '14px',
        textAlign: 'left',
    })
)

export const DiscountBannerBox = styled(Box)(({ theme }) => ({
    position: 'relative',
    zIndex: 1,
    lineHeight: 1,
    "img": {
        borderRadius: '5px',
        height: "auto",
        maxHeight: '250px',
    },
    '&::after': {
        content: '" "',
        position: 'absolute',
        width: '100%',
        height: 'calc(100% - 3px)',
        borderRadius: '5px',
        left: '0',
        backgroundColor: alpha(theme.palette.primary.dark, 0.1),
        zIndex: '-1',
        top: '1px',
    },
}))

export const LandingHeroBox = styled(Box)(
    ({ theme, isXSmall }) => ({
        display: 'flex',
        marginInline: isXSmall && '15px',
        borderRadius: isXSmall && '5px',
        position: 'relative',
        zIndex: 1,
        '> img': {
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: isXSmall && '5px',
            position: 'absolute',
            zIndex: -1,
            bottom: 0,
            left: 0,
        },
    })
)
