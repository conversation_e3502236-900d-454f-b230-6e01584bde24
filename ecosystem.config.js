module.exports = {
  apps: [
    {
      name: 'stackfood-react-cluster',
      script: 'npm',
      args: 'start',
      cwd: '/var/www/html/react-stackfood_2',
      instances: 2, // Start with 2 instances
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NEXT_TELEMETRY_DISABLED: 1
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
        NEXT_TELEMETRY_DISABLED: 1
      },
      // Performance settings
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      
      // Restart settings
      autorestart: true,
      watch: false,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Logging
      log_file: '/var/log/pm2/stackfood-react-combined.log',
      out_file: '/var/log/pm2/stackfood-react-out.log',
      error_file: '/var/log/pm2/stackfood-react-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Health monitoring
      listen_timeout: 10000,
      kill_timeout: 5000,
      
      // Remove wait_ready to avoid port conflicts
      increment_var: 'PORT'
    }
  ]
}
