import { initializeApp, getApps, getApp } from 'firebase/app'
import {
    getMessaging,
    getToken,
    onMessage,
    isSupported,
} from 'firebase/messaging'
import { getAuth } from 'firebase/auth'

const firebaseConfig = {
    apiKey: 'AIzaSyB8uHM9tXLWgA7QSyum-6RpbpUFRXBHnU0',
    authDomain: 'delivery-add06.firebaseapp.com',
    projectId: 'delivery-add06',
    storageBucket: 'delivery-add06.firebasestorage.app',
    messagingSenderId: '13815378193',
    appId: '1:13815378193:web:2b49f7173257c55771dcb0',
    measurementId: 'G-LG6T8S8V10',
}
const firebaseApp = !getApps().length ? initializeApp(firebaseConfig) : getApp()
const messaging = (async () => {
    try {
        const isSupportedBrowser = await isSupported()
        if (isSupportedBrowser) {
            return getMessaging(firebaseApp)
        }

        return null
    } catch (err) {
        return null
    }
})()

export const fetchToken = async (setFcmToken) => {
    return getToken(await messaging, {
        vapidKey: 'BAoSMg62g1OvPPI2LQodXwIFXHgzovTtrW5FZNalqD_-1vbGQ8etOonRoMBSFozHtlWKqeJngcm1atK8BcQfd6c',
    })
        .then((currentToken) => {
            if (currentToken) {
                setFcmToken(currentToken)
            } else {
                setFcmToken()
            }
        })
        .catch((err) => {
            console.error(err)
        })
}

export const onMessageListener = async () =>
    new Promise((resolve) =>
        (async () => {
            const messagingResolve = await messaging
            onMessage(messagingResolve, (payload) => {
                resolve(payload)
            })
        })()
    )
export const auth = getAuth(firebaseApp)
