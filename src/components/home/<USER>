import { styled } from '@mui/material/styles'
import { Box, Card, Typography, Stack } from '@mui/material'
import map from '../../assets/images/map.png'

export const LeftArrowStyle = styled(Box, {
    shouldForwardProp: (prop) =>
        prop !== 'languageDirection' &&
        prop !== 'left' &&
        prop !== 'isdisabled',
})(({ theme, languageDirection, left, isdisabled }) => ({
    zIndex: 999,
    top: '42%',
    position: 'absolute',
    display: isdisabled ? 'none' : 'block',
    left: languageDirection === 'rtl' ? '2%' : left,
    [theme.breakpoints.down('sm')]: {
        display: 'none',
    },
}))

export const RightArrowStyle = styled(Box, {
    shouldForwardProp: (prop) =>
        prop !== 'languageDirection' &&
        prop !== 'right' &&
        prop !== 'isdisabled',
})(({ theme, languageDirection, right, isdisabled }) => ({
    zIndex: 999,
    position: 'absolute',
    top: '42%',
    right: languageDirection === 'rtl' ? '2%' : right,
    display: isdisabled ? 'none' : 'block',

    [theme.breakpoints.down('sm')]: {
        display: 'none',
    },
}))

export const HomeTitleTypography = styled(Typography)(({ theme }) => ({
    fontWeight: '800',
    color: `${theme.palette.mode === 'dark' && '#fff'}`,
}))
export const HomeTextTypography = styled(Typography)(({ theme }) => ({
    fontSize: '14px',
    fontWeight: '600',
    lineHeight: '22px',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    maxWidth: '140px',

    color: `${theme.palette.mode === 'dark' && '#fff'}`,
    [theme.breakpoints.down('sm')]: {
        fontSize: '12px',
    },
}))
export const PopularRestaurantCard = styled(Card)(({ theme }) => ({
    margin: '20px 0',
    padding: '30px',
    boxShadow: `${
        theme.palette.mode === 'light' &&
        '0px 0px 2px rgba(239, 120, 34, 0.1),0px 6px 12px rgba(239, 120, 34, 0.08)'
    }`,
    borderRadius: '10px',
    color: `${theme.palette.mode === 'dark' && '#fff'}`,
    [theme.breakpoints.down('sm')]: {
        padding: '7px',
        margin: '0px 0',
    },
}))
export const FoodDetailModalStyle = styled(Box)(({ theme }) => ({
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    maxWidth: '469px',
    width: '100%',
    borderRadius: '5px',
    boxShadow: 24,
    border: 'none',

    color: `${theme.palette.mode === 'dark' && '#fff'}`,
    [theme.breakpoints.down('md')]: {
        width: '85%',
    },
    [theme.breakpoints.down('xs')]: {
        width: '85%',
    },
}))

export const CustomSpinner = styled(Stack)(({ theme, color }) => ({
    position: 'relative',
    width: '10px',
    height: '10px',
    borderRadius: '5px',
    backgroundColor: 'transparent',
    color: 'transparent',
    boxShadow:
        '0 -18px 0 0 ${color}, 12.727926px -12.727926px 0 0 ${color}, 18px 0 0 0 ${color}, 12.727926px 12.727926px 0 0 rgba(152, 128, 255, 0), 0 18px 0 0 rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 0 rgba(152, 128, 255, 0), -18px 0 0 0 rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 0 rgba(152, 128, 255, 0)',
    animation: 'dots-pin 1.5s infinite linear',
    '@Keyframes dots-pin': {
        '0%': {
            boxShadow: `0 -18px 0 0 ${color}, 12.727926px -12.727926px 0 0 ${color}, 18px 0 0 0 ${color}, 12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), 0 18px 0 -5px rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), -18px 0 0 -5px rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0)`,
        },
        '100%': {
            boxShadow: `0 -18px 0 0 ${color}, 12.727926px -12.727926px 0 0 ${color}, 18px 0 0 0 ${color}, 12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), 0 18px 0 -5px rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), -18px 0 0 -5px rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0)`,
        },
        '12.5%': {
            boxShadow: `0 -18px 0 -5px rgba(152, 128, 255, 0), 12.727926px -12.727926px 0 0 ${color}, 18px 0 0 0 ${color}, 12.727926px 12.727926px 0 0 ${color}, 0 18px 0 -5px rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), -18px 0 0 -5px rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0)`,
        },
        '25%': {
            boxShadow: `0 -18px 0 -5px rgba(152, 128, 255, 0), 12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0), 18px 0 0 0 ${color}, 12.727926px 12.727926px 0 0 ${color}, 0 18px 0 0 ${color}, -12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), -18px 0 0 -5px rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0)`,
        },
        '37.5%': {
            boxShadow: `0 -18px 0 -5px rgba(152, 128, 255, 0), 12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0), 18px 0 0 -5px rgba(152, 128, 255, 0), 12.727926px 12.727926px 0 0 ${color}, 0 18px 0 0 ${color}, -12.727926px 12.727926px 0 0 ${color}, -18px 0 0 -5px rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0)`,
        },
        '50%': {
            boxShadow: `0 -18px 0 -5px rgba(152, 128, 255, 0), 12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0), 18px 0 0 -5px rgba(152, 128, 255, 0), 12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), 0 18px 0 0 ${color}, -12.727926px 12.727926px 0 0 ${color}, -18px 0 0 0 ${color}, -12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0)`,
        },
        '62.5%': {
            boxShadow: `0 -18px 0 -5px rgba(152, 128, 255, 0), 12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0), 18px 0 0 -5px rgba(152, 128, 255, 0), 12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), 0 18px 0 -5px rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 0 ${color}, -18px 0 0 0 ${color}, -12.727926px -12.727926px 0 0 ${color}`,
        },
        '75%': {
            boxShadow: `0 -18px 0 0 ${color}, 12.727926px -12.727926px 0 -5px rgba(152, 128, 255, 0), 18px 0 0 -5px rgba(152, 128, 255, 0), 12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), 0 18px 0 -5px rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), -18px 0 0 0 ${color}, -12.727926px -12.727926px 0 0 ${color}`,
        },
        '87.5%': {
            boxShadow: `0 -18px 0 0 ${color}, 12.727926px -12.727926px 0 0 ${color}, 18px 0 0 -5px rgba(152, 128, 255, 0), 12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), 0 18px 0 -5px rgba(152, 128, 255, 0), -12.727926px 12.727926px 0 -5px rgba(152, 128, 255, 0), -18px 0 0 -5px rgba(152, 128, 255, 0), -12.727926px -12.727926px 0 0 ${color}`,
        },
    },
}))

export const MapSetionWrapper = styled(Stack)(({ theme }) => ({
     backgroundImage: `url(${map.src})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    borderRadius: '16px',

    [theme.breakpoints.down('sm')]: {
        height: '340px',
    },
    '.map-overly': {
        height: '425px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '16px',
        background: 'rgba(20, 19, 19, 0.5)',
        gap: '50px',
    },
}))
export const VisitAgainWrapper = styled(Stack)(({ theme }) => ({
    borderRadius: '16px',
    alignItems: 'center',
    paddingBlock: '30px',
    gap: '30px',
    backgroundColor: theme.palette.customColor.thirteen,
    '& .slick-dots': {
        position: 'absolute !important',
        bottom: '-35px !important',
        display: 'block',
        width: '100% !important',
        padding: 0,
        margin: 0,
        textAlign: 'center !important',
    },
    [theme.breakpoints.down('sm')]: {
        paddingBlock: '18px',
        gap: '16px',
    },
    '& .slick-track': {
        gap: '30px',
    },
}))
